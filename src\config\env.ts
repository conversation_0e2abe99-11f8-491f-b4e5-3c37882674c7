/**
 * Environment variable validation and configuration
 * Provides type-safe access to environment variables with validation
 */

interface EnvConfig {
  apiUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
}

/**
 * Validates and returns environment configuration
 * Throws error if required environment variables are missing
 */
function validateEnv(): EnvConfig {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;

  if (!apiUrl) {
    throw new Error("NEXT_PUBLIC_API_URL environment variable is required");
  }

  return {
    apiUrl,
    isDevelopment: process.env.NODE_ENV === "development",
    isProduction: process.env.NODE_ENV === "production",
  };
}

export const env = validateEnv();

/**
 * API URL helper - constructs full API endpoint URLs
 */
export const getApiUrl = (endpoint: string = ""): string => {
  return `${env.apiUrl}${endpoint}`;
};

/**
 * Default authentication configuration
 * Based on API documentation requirements
 */
export const DEFAULT_AUTH_CONFIG = {
  apiUrl: env.apiUrl,
  endpoints: {
    login: "/admin/auth/login",
    profile: "/admin/auth/profile",
    register: "/admin/auth/register",
    testPermissions: "/admin/auth/test-permissions",
    testRoles: "/admin/auth/test-roles",
  },
  headers: {
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
    "X-Afreeserv-Client": "admin-panel",
  },
  keys: {
    accessToken: "access_token",
    refreshToken: "refresh_token",
    user: "user",
    expiresAt: "expires_at",
  },
};
