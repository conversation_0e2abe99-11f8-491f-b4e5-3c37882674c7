/**
 * Authentication Actions Index
 * Centralized exports for all authentication-related server actions
 * Organized by responsibility following clean code principles
 */

// Authentication Operations
export {
  authenticateUser,
  logoutUser,
  refreshUserToken,
} from "./authentication-actions";

// Session Management
export {
  isUserAuthenticated,
  getCurrentUserSession,
  validateAuthToken,
  getSessionData,
  updateUserSession,
  clearUserSession,
} from "./session-actions";

// Legacy exports for backward compatibility
// These will be deprecated in favor of the new organized structure
export { authenticateUser as login } from "./authentication-actions";
export { logoutUser as logout } from "./authentication-actions";
export { refreshUserToken as refreshAuthToken } from "./authentication-actions";
export { getCurrentUserSession as getCurrentUser } from "./session-actions";
export { isUserAuthenticated as getIsAuthenticated } from "./session-actions";
export { validateAuthToken as validateToken } from "./session-actions";
