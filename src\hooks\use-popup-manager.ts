"use client";

import { useState, useCallback } from "react";

export function usePopupManager(): PopupManagerActions {
  const [state, setState] = useState<PopupManagerState>({
    isOpen: false,
    config: null,
  });

  const create = useCallback((config: PopupConfig) => {
    console.log("Popup manager: Creating popup", {
      title: config.title,
      hasInput: !!config.input,
      timestamp: new Date().toISOString(),
    });

    // Call onOpen callback if provided
    config.onOpen?.();

    setState({
      isOpen: true,
      config,
    });
  }, []);

  const close = useCallback(() => {
    console.log("Popup manager: Closing popup", {
      timestamp: new Date().toISOString(),
    });

    // Call onClose callback if provided
    state.config?.onClose?.();

    setState({
      isOpen: false,
      config: null,
    });
  }, [state.config]);

  return {
    create,
    close,
    isOpen: state.isOpen,
    // Expose the current config and state for the popup component
    ...(state.isOpen &&
      state.config && {
        config: state.config,
      }),
  } as PopupManagerActions & { config?: PopupConfig };
}
