import "../support/e2e";
import "../../src/app/[locale]/globals.css";
import React from "react";
import { mount } from "@cypress/react";
import { NextIntlClientProvider } from "next-intl";
import enMessages from "../../messages/en.json";

// Augment the Cypress namespace to include our custom mount command
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
    }
  }
}

// Custom mount command that includes all necessary providers
Cypress.Commands.add("mount", (component, options = {}) => {
  return mount(
    <NextIntlClientProvider messages={enMessages} locale="en">
      {component}
    </NextIntlClientProvider>,
    options,
  );
});
