"use client";

import { listServicesAction } from "@/actions";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { ServicesTable } from "./services-table";
import { ServicesTableSkeleton } from "./services-table-skeleton";
import { ServicesTableError } from "./services-table-error";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { GlobalQueryParamsType } from "@/types/global";

/**
 * Client-side component with React Query integration
 * Follows the established React Query + Server Actions pattern
 */
export function AsyncServicesTable() {
  const { searchParams } = useGlobalQueryParams();

  // Use the exact pattern from best practices guide
  const query = useMemo(() => searchParams, [searchParams]);

  // Create query key following best practices pattern
  const queryKey = useMemo(() => ["services", "list", query], [query]);

  const { data, isLoading, error } = useQuery({
    queryKey,
    queryFn: async () => {
      const result = await listServicesAction(
        query as unknown as GlobalQueryParamsType,
      );
      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes - as per best practices
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
  });

  if (error) {
    return <ServicesTableError error={error} />;
  }

  if (isLoading) {
    return <ServicesTableSkeleton />;
  }

  // Extract services and total items from response
  const services = data?.response?.items || [];
  const totalItems = data?.response?.nb_items || 0;

  return <ServicesTable services={services} totalItems={totalItems} />;
}
