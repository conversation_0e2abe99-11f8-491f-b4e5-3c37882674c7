import { useTranslations } from "next-intl";
import { ChevronLeft } from "lucide-react";

interface ServiceHeaderProps {
  onBack: () => void;
}

export function ServiceHeader({ onBack }: ServiceHeaderProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <div className="flex justify-between items-center w-full gap-14 px-2.5 py-2">
      <div className="flex items-center gap-2.5">
        <button
          onClick={onBack}
          className="flex justify-center items-center w-[35px] h-[35px] bg-background border border-border rounded-full hover:bg-gray-50 transition-colors"
        >
          <ChevronLeft className="w-[10.5px] h-[17.5px] text-foreground" />
        </button>
        <span className="text-sm font-medium text-foreground font-['Poppins']">
          {t("backToServices")}
        </span>
      </div>
      <div className="w-6 h-6" />
    </div>
  );
}
