"use client";

import { useTranslations } from "next-intl";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";

interface PricingDetailsProps {
  pricing: Pricing;
}

export function PricingDetails({ pricing }: PricingDetailsProps) {
  const t = useTranslations("ServiceDetails");
  const { searchParams } = useGlobalQueryParams();

  // Get selected currency from global params, default to service's original currency
  const currentCurrency = searchParams.currency || pricing.iso_curry;

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
      minimumFractionDigits: 2,
    }).format(price);
  };

  // Currency conversion logic based on API response
  const getDisplayPrice = (): { price: number; currency: string } => {
    const targetCurrency = currentCurrency.toUpperCase();
    const originalCurrency = pricing.iso_curry.toUpperCase();

    // If same currency, return original price
    if (targetCurrency === originalCurrency) {
      return { price: pricing.price, currency: originalCurrency };
    }

    // If target is EUR and we have priceEuro from API
    if (targetCurrency === "EUR" && pricing.priceEuro) {
      return { price: pricing.priceEuro, currency: "EUR" };
    }

    // If original is EUR and target is different, use conversion rates
    if (originalCurrency === "EUR") {
      const conversionRates: Record<string, number> = {
        USD: 1.1, // 1 EUR = 1.1 USD (example rates)
        GBP: 0.85, // 1 EUR = 0.85 GBP
        XAF: 655.96, // 1 EUR = 655.96 XAF
      };

      const rate = conversionRates[targetCurrency];
      if (rate) {
        return { price: pricing.price * rate, currency: targetCurrency };
      }
    }

    // Fallback: return original price with note
    return { price: pricing.price, currency: originalCurrency };
  };

  const { price, currency } = getDisplayPrice();

  return (
    <div className="flex flex-col w-full">
      <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
        {t("price")} :
      </label>
      <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full">
        <span className="text-xs text-foreground font-['Poppins']">
          {formatPrice(price, currency)} ({pricing.pricingModel})
          {currency !== currentCurrency.toUpperCase() && (
            <span className="text-foreground opacity-70 ml-1">
              (Original: {formatPrice(pricing.price, pricing.iso_curry)})
            </span>
          )}
        </span>
      </div>
    </div>
  );
}
