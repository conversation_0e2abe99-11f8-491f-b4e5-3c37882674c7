"use server";

import { authService } from "@/services/auth";
import { CookieStorage } from "@/services/auth/token-storage";
import { FormLoginData } from "@/schemas";
import {
  createAuthError,
  handleNetworkError,
  logAuthError,
  getErrorDisplayMessage,
} from "@/lib/auth-error-handler";

/**
 * Authentication Actions
 * Focused on authentication operations: login, logout, token refresh
 * Following Single Responsibility Principle from clean code practices
 */

/**
 * Server action for user login
 * Handles authentication and cookie storage with proper error handling
 */
export async function authenticateUser(
  credentials: FormLoginData,
): Promise<LoginResponse> {
  try {
    // Call the auth service for pure API communication
    const authResponse = await authService.login(credentials);

    if (!authResponse) {
      const authError = createAuthError.invalidCredentials(
        "Invalid credentials or login failed",
      );
      logAuthError(authError, "authenticateUser");

      return {
        success: false,
        error: {
          code: authError.code,
          message: getErrorDisplayMessage(authError),
        },
      };
    }

    // Handle cookie storage on server side
    await CookieStorage.setTokens(authResponse);

    return {
      success: true,
      data: {
        admin: authResponse.admin,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
        message: "Login successful",
      },
    };
  } catch (error) {
    const authError = handleNetworkError(error);
    logAuthError(authError, "authenticateUser");

    return {
      success: false,
      error: {
        code: authError.code,
        message: getErrorDisplayMessage(authError),
        details: authError.details,
      },
    };
  }
}

/**
 * Server action for user logout
 * Handles cookie clearing on server side
 */
export async function logoutUser(): Promise<LogoutResponse> {
  try {
    // Clear cookies on server side
    await CookieStorage.clear();

    // Call the auth service for any API cleanup
    const result = await authService.logout();

    return result;
  } catch (error) {
    console.error("Logout error:", error);
    return {
      success: false,
      message: "An error occurred during logout",
    };
  }
}

/**
 * Server action for token refresh
 * Handles token refresh and cookie updates
 */
export async function refreshUserToken(): Promise<LoginResponse> {
  try {
    // Get current refresh token from cookies
    const refreshToken = await CookieStorage.getRefreshToken();

    if (!refreshToken) {
      return {
        success: false,
        error: {
          code: "NO_REFRESH_TOKEN",
          message: "No refresh token found",
        },
      };
    }

    // Call auth service for token refresh
    const authResponse = await authService.refreshToken(refreshToken);

    if (!authResponse) {
      return {
        success: false,
        error: {
          code: "REFRESH_ERROR",
          message: "Token refresh failed",
        },
      };
    }

    // Update cookies with new tokens
    await CookieStorage.setTokens(authResponse);

    return {
      success: true,
      data: {
        admin: authResponse.admin,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
        message: "Token refreshed successfully",
      },
    };
  } catch (error) {
    console.error("Token refresh error:", error);
    return {
      success: false,
      error: {
        code: "REFRESH_ERROR",
        message: "An error occurred during token refresh",
        details:
          error instanceof Error
            ? { message: error.message, stack: error.stack }
            : { error: String(error) },
      },
    };
  }
}
