import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { PaginationButton } from "./pagination-button";
import { PaginationNextButton } from "./pagination-next-button";
import { getVisiblePages } from "./pagination-utils";

interface TablePaginationProps {
  totalItems: number;
  totalPages: number;
}

export function TablePagination({ totalPages }: TablePaginationProps) {
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  if (totalPages <= 1) {
    return null;
  }

  const currentPage = searchParams.next || 1;

  const handlePageChange = (page: number) => {
    setSearchParams({ ...searchParams, next: page });
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setSearchParams({ ...searchParams, next: currentPage + 1 });
    }
  };

  return (
    <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[282x] h-[48px]">
      {/* Spacer for alignment */}
      <div className="w-6 h-6" />

      {/* Page Numbers */}
      <div className="flex items-center gap-3">
        {getVisiblePages(currentPage, totalPages).map((page, index) => {
          if (page === "...") {
            return (
              <span
                key={`ellipsis-${index}`}
                className="text-[#373737] text-xs"
              >
                ...
              </span>
            );
          }

          const pageNumber = page as number;
          const isActive = pageNumber === currentPage;

          return (
            <PaginationButton
              key={pageNumber}
              pageNumber={pageNumber}
              isActive={isActive}
              onClick={() => handlePageChange(pageNumber)}
            />
          );
        })}
      </div>

      {/* Next Button */}
      <PaginationNextButton
        onClick={handleNextPage}
        disabled={currentPage >= totalPages}
      />
    </div>
  );
}
