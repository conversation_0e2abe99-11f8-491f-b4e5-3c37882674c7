import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui";

interface ServiceStatusAndDatesProps {
  service: Service;
}

export function ServiceStatusAndDates({ service }: ServiceStatusAndDatesProps) {
  const t = useTranslations("ServiceDetails");

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "default";
      case "inactive":
        return "secondary";
      case "pending":
        return "outline";
      case "rejected":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="flex w-full p-3 items-center border-b border-border">
      <div className="flex-1">
        <span className="text-sm font-medium text-foreground font-['Poppins']">
          {t("serviceDetails")}
        </span>
      </div>
      <div className="flex items-center space-x-5">
        <div className="flex items-center gap-2.5">
          <span className="text-xs text-foreground font-['Poppins']">
            {t("statusLabel")}
          </span>
          <span className="text-xs font-medium text-foreground font-['Poppins']">
            {service.status}
          </span>
          <Badge variant={getStatusVariant(service.status)}>
            {t(`status.${service.status.toLowerCase()}` as never)}
          </Badge>
        </div>
        <div className="flex items-center gap-2.5">
          <span className="text-xs text-foreground font-['Poppins']">
            {t("serviceIdLabel")}
          </span>
          <span className="text-xs font-medium text-foreground font-['Poppins']">
            S{service.id}
          </span>
        </div>
      </div>
    </div>
  );
}
