import { TruncatedText } from "@/components/common";

export const providerColumn: NativeTableColumn<Service> = {
  id: "provider",
  accessorKey: "provider",
  header: "Fournisseur",
  enableSorting: true,
  cell: ({ value }) => {
    const provider = value as Provider;
    return (
      <div className="text-sm">
        <div className="font-medium">
          <TruncatedText
            maxWidth="150"
            text={`${provider.firstName} ${provider.lastName}`}
          />
        </div>
        <TruncatedText maxWidth="150" text={provider.extl_id} />
      </div>
    );
  },
  size: 160,
};
