import { z } from "zod";

/**
 * Login form validation schema
 * Updated to use email field (API-compliant) instead of username
 * Following clean code principles with consistent naming and error messages
 */

// ============================================================================
// ERROR MESSAGE CONSTANTS
// ============================================================================

/**
 * Standardized error message patterns
 * Consistent format across all form validations
 */
const ERROR_MESSAGES = {
  EMAIL_REQUIRED: "email_required",
  EMAIL_INVALID_FORMAT: "email_invalid_format",
  EMAIL_TOO_LONG: "email_too_long",
  PASSWORD_REQUIRED: "password_required",
  PASSWORD_MIN_LENGTH: "password_min_length_6",
  PASSWORD_TOO_LONG: "password_too_long",
} as const;

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

/**
 * Validation constraints matching API requirements
 */
const VALIDATION_RULES = {
  EMAIL_MAX_LENGTH: 255,
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 100,
} as const;

// ============================================================================
// SCHEMA DEFINITION
// ============================================================================

/**
 * Login form schema with email-based authentication
 * Matches AdminLoginRequest interface from API specification
 */
export const FormLoginSchema = z.object({
  email: z
    .string({ required_error: ERROR_MESSAGES.EMAIL_REQUIRED })
    .min(1, { message: ERROR_MESSAGES.EMAIL_REQUIRED })
    .email({ message: ERROR_MESSAGES.EMAIL_INVALID_FORMAT })
    .max(VALIDATION_RULES.EMAIL_MAX_LENGTH, {
      message: ERROR_MESSAGES.EMAIL_TOO_LONG,
    }),

  password: z
    .string({ required_error: ERROR_MESSAGES.PASSWORD_REQUIRED })
    .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, {
      message: ERROR_MESSAGES.PASSWORD_MIN_LENGTH,
    })
    .max(VALIDATION_RULES.PASSWORD_MAX_LENGTH, {
      message: ERROR_MESSAGES.PASSWORD_TOO_LONG,
    }),
});

/**
 * Inferred type from login schema
 * Following consistent naming convention (Data suffix instead of Type)
 */
export type FormLoginData = z.infer<typeof FormLoginSchema>;

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Standalone email validation helper
 * Useful for real-time validation in components
 */
export const validateEmail = (email: string): boolean => {
  return FormLoginSchema.shape.email.safeParse(email).success;
};

/**
 * Standalone password validation helper
 * Useful for real-time validation in components
 */
export const validatePassword = (password: string): boolean => {
  return FormLoginSchema.shape.password.safeParse(password).success;
};
