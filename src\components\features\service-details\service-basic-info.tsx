import { useTranslations } from "next-intl";

interface ServiceBasicInfoProps {
  service: Service;
}

export function ServiceBasicInfo({ service }: ServiceBasicInfoProps) {
  const t = useTranslations("ServiceDetails");

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t("notAvailable");
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return t("invalidDate");
    }
  };

  return (
    <div className="flex flex-col w-full">
      <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
        {t("createdAt")} :
      </label>
      <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full">
        <span className="text-xs text-foreground font-['Poppins']">
          {formatDate(service.createdAt)}
        </span>
      </div>
    </div>
  );
}
