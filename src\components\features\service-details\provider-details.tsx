import { useTranslations } from "next-intl";

interface ProviderDetailsProps {
  provider: Provider;
}

export function ProviderDetails({ provider }: ProviderDetailsProps) {
  const t = useTranslations("ServiceDetails");

  const providerInfoItems: string[] = [
    `ID : #${provider.id}`,
    `${provider.firstName} ${provider.lastName}`,
    provider.email ?? "",
    provider.unsafe_metadata?.username ?? "",
  ];

  const popularityItems: string[] = [
    `${t("country")} : ${provider.unsafe_metadata?.postal_address?.country ?? "France"}`,
    `${t("city")} : ${provider.unsafe_metadata?.postal_address?.city ?? "Paris"}`,
  ];

  const renderItems = (items: string[]) => {
    return items.map((item, index) => (
      <div
        key={index}
        className="flex flex-row items-center gap-2.5 px-2.5 h-auto"
      >
        <span className="font-['Poppins'] font-normal text-xs leading-[2.08em] text-left text-foreground">
          {item}
        </span>
      </div>
    ));
  };

  return (
    <>
      {/* Provider Information Section */}
      <div className="flex flex-col gap-2.5 flex-1">
        {/* Header */}
        <div className="bg-secondary flex flex-row justify-between items-center gap-2.5 px-2.5 h-auto">
          <span className="font-['Poppins'] font-medium text-xs leading-[2.08em] text-left text-foreground">
            {t("providerInformation")}
          </span>
        </div>

        {/* Content Items */}
        {renderItems(providerInfoItems)}
      </div>

      {/* Service Popularity Section */}
      <div className="flex flex-col gap-2.5 flex-1">
        {/* Header */}
        <div className="bg-secondary flex flex-row justify-between items-center gap-2.5 px-2.5 h-auto">
          <span className="font-['Poppins'] font-medium text-xs leading-[2.08em] text-left text-foreground">
            {t("servicePopularity")}
          </span>
        </div>

        {/* Content Items */}
        {renderItems(popularityItems)}
      </div>
    </>
  );
}
