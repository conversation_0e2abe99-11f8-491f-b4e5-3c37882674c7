"use client";

import React, { Component, ReactNode } from "react";
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

/**
 * Authentication error types
 */
export enum AuthErrorType {
  TOKEN_EXPIRED = "TOKEN_EXPIRED",
  TOKEN_INVALID = "TOKEN_INVALID",
  NETWORK_ERROR = "NETWORK_ERROR",
  PERMISSION_DENIED = "PERMISSION_DENIED",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

/**
 * Authentication error class
 */
export class AuthError extends Error {
  public readonly type: AuthErrorType;
  public readonly code?: string;
  public readonly details?: unknown;

  constructor(
    message: string,
    type: AuthErrorType = AuthErrorType.UNKNOWN_ERROR,
    code?: string,
    details?: unknown,
  ) {
    super(message);
    this.name = "AuthError";
    this.type = type;
    this.code = code;
    this.details = details;
  }
}

/**
 * Error boundary props
 */
interface AuthErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AuthError, retry: () => void) => ReactNode;
  onError?: (error: AuthError) => void;
}

/**
 * Error boundary state
 */
interface AuthErrorBoundaryState {
  hasError: boolean;
  error: AuthError | null;
}

/**
 * Authentication error boundary
 * Catches and handles authentication-related errors
 * Follows clean code principles with proper error handling
 */
export class AuthErrorBoundary extends Component<
  AuthErrorBoundaryProps,
  AuthErrorBoundaryState
> {
  constructor(props: AuthErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): AuthErrorBoundaryState {
    // Convert regular errors to AuthError if needed
    const authError =
      error instanceof AuthError
        ? error
        : new AuthError(error.message, AuthErrorType.UNKNOWN_ERROR);

    return {
      hasError: true,
      error: authError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Authentication error caught by boundary:", error, errorInfo);

    const authError =
      error instanceof AuthError
        ? error
        : new AuthError(error.message, AuthErrorType.UNKNOWN_ERROR);

    this.props.onError?.(authError);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.retry);
      }

      return (
        <DefaultAuthErrorFallback error={this.state.error} retry={this.retry} />
      );
    }

    return this.props.children;
  }
}

/**
 * Default error fallback component
 */
interface DefaultAuthErrorFallbackProps {
  error: AuthError;
  retry: () => void;
}

function DefaultAuthErrorFallback({
  error,
  retry,
}: DefaultAuthErrorFallbackProps) {
  const getErrorMessage = (
    error: AuthError,
  ): { title: string; description: string } => {
    switch (error.type) {
      case AuthErrorType.TOKEN_EXPIRED:
        return {
          title: "Session Expired",
          description:
            "Your session has expired. Please log in again to continue.",
        };
      case AuthErrorType.TOKEN_INVALID:
        return {
          title: "Invalid Session",
          description: "Your session is invalid. Please log in again.",
        };
      case AuthErrorType.NETWORK_ERROR:
        return {
          title: "Connection Error",
          description:
            "Unable to connect to the authentication server. Please check your internet connection and try again.",
        };
      case AuthErrorType.PERMISSION_DENIED:
        return {
          title: "Access Denied",
          description: "You don't have permission to access this resource.",
        };
      default:
        return {
          title: "Authentication Error",
          description:
            error.message || "An unexpected authentication error occurred.",
        };
    }
  };

  const { title, description } = getErrorMessage(error);

  const handleLogout = () => {
    // Clear local storage and redirect to login
    localStorage.clear();
    window.location.href = "/login";
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="max-w-md w-full space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{title}</AlertTitle>
          <AlertDescription>{description}</AlertDescription>
        </Alert>

        <div className="flex space-x-2">
          {error.type === AuthErrorType.NETWORK_ERROR && (
            <Button onClick={retry} variant="outline" className="flex-1">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}

          <Button onClick={handleLogout} className="flex-1">
            <LogOut className="h-4 w-4 mr-2" />
            {error.type === AuthErrorType.TOKEN_EXPIRED ||
            error.type === AuthErrorType.TOKEN_INVALID
              ? "Log In Again"
              : "Go to Login"}
          </Button>
        </div>

        {process.env.NODE_ENV === "development" && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm text-gray-500">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(
                {
                  type: error.type,
                  code: error.code,
                  message: error.message,
                  details: error.details,
                },
                null,
                2,
              )}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

/**
 * Hook for throwing authentication errors
 */
export function useAuthError() {
  const throwAuthError = (
    message: string,
    type: AuthErrorType = AuthErrorType.UNKNOWN_ERROR,
    code?: string,
    details?: unknown,
  ) => {
    throw new AuthError(message, type, code, details);
  };

  return { throwAuthError };
}

/**
 * Utility function to create authentication errors
 */
export const createAuthError = {
  tokenExpired: (details?: unknown) =>
    new AuthError(
      "Token has expired",
      AuthErrorType.TOKEN_EXPIRED,
      "TOKEN_EXPIRED",
      details,
    ),

  tokenInvalid: (details?: unknown) =>
    new AuthError(
      "Token is invalid",
      AuthErrorType.TOKEN_INVALID,
      "TOKEN_INVALID",
      details,
    ),

  networkError: (details?: unknown) =>
    new AuthError(
      "Network connection failed",
      AuthErrorType.NETWORK_ERROR,
      "NETWORK_ERROR",
      details,
    ),

  permissionDenied: (resource?: string, details?: unknown) =>
    new AuthError(
      `Access denied${resource ? ` to ${resource}` : ""}`,
      AuthErrorType.PERMISSION_DENIED,
      "PERMISSION_DENIED",
      details,
    ),

  unknown: (message: string, details?: unknown) =>
    new AuthError(
      message,
      AuthErrorType.UNKNOWN_ERROR,
      "UNKNOWN_ERROR",
      details,
    ),
};

/**
 * Higher-order component for wrapping components with auth error boundary
 */
export function withAuthErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<AuthErrorBoundaryProps, "children">,
) {
  return function WrappedComponent(props: P) {
    return (
      <AuthErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </AuthErrorBoundary>
    );
  };
}
