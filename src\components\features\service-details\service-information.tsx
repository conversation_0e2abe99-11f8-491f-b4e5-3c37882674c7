"use client";

import { useTranslations } from "next-intl";
import { usePopup } from "@/providers/popup-provider";
import { ServiceBasicInfo } from "./service-basic-info";
import { ServiceDescriptions } from "./service-descriptions";
import { ServiceCategoriesAndLocations } from "./service-categories-and-locations";
import { ServiceStatusAndDates } from "./service-status-and-dates";
import { ServiceImages } from "./service-images";
import { PricingDetails } from "./pricing-details";

interface ServiceInformationProps {
  service: Service;
}

export function ServiceInformation({ service }: ServiceInformationProps) {
  const t = useTranslations("ServiceDetails");
  const popup = usePopup();

  const handleReject = () => {
    popup.create({
      title: t("rejectionReason"),
      size: "4xl",
      input: {
        placeholder: t("rejectionReasonLabel"),
        required: true,
        rows: 3,
        validation: (value: string) => value.trim().length > 0,
      },
      actions: {
        cancel: {
          label: t("cancel").toUpperCase(),
          onClick: () => {
            console.log("Service rejection cancelled:", {
              serviceId: service.id,
              serviceName: service.name,
              providerId: service.provider.id,
              timestamp: new Date().toISOString(),
            });
          },
        },
        confirm: {
          label: t("send").toUpperCase(),
          onClick: (rejectionReason?: string) => {
            console.log("Service rejected:", {
              serviceId: service.id,
              serviceName: service.name,
              providerId: service.provider.id,
              providerName: `${service.provider.firstName} ${service.provider.lastName}`,
              rejectionReason: rejectionReason || "",
              timestamp: new Date().toISOString(),
            });
            // Here you would typically call an API to reject the service
            // Example: rejectService(service.id, rejectionReason || "");
          },
        },
      },
      onOpen: () => {
        console.log("Rejection popup opened:", {
          serviceId: service.id,
          serviceName: service.name,
          timestamp: new Date().toISOString(),
        });
      },
    });
  };

  const handleDelete = () => {
    popup.create({
      title: "Delete Service",
      size: "lg",
      description:
        "Are you sure you want to delete this service? This action cannot be undone.",
      actions: {
        cancel: {
          label: t("cancel").toUpperCase(),
          onClick: () => {
            console.log("Service deletion cancelled:", {
              serviceId: service.id,
              serviceName: service.name,
              timestamp: new Date().toISOString(),
            });
          },
        },
        confirm: {
          label: t("delete").toUpperCase(),
          onClick: () => {
            console.log("Service deleted:", {
              serviceId: service.id,
              serviceName: service.name,
              timestamp: new Date().toISOString(),
            });
            // Here you would typically call an API to delete the service
            // Example: deleteService(service.id);
          },
        },
      },
    });
  };

  const handleValidate = () => {
    console.log("Service validated:", {
      serviceId: service.id,
      serviceName: service.name,
      timestamp: new Date().toISOString(),
    });
    // Here you would typically call an API to validate the service
    // Example: validateService(service.id);
  };

  return (
    <div className="w-full bg-background border">
      {/* Header */}
      <div className="flex flex-col p-0 border-b border-border rounded-t-[10px]">
        <ServiceStatusAndDates service={service} />
      </div>

      {/* Content */}
      <div className="flex flex-col gap-6 px-5 py-2.5">
        <ServiceBasicInfo service={service} />
        <ServiceDescriptions service={service} />
        <ServiceCategoriesAndLocations service={service} />
        <ServiceImages images={service.img} serviceName={service.name} />
        <PricingDetails pricing={service.pricing} />

        {/* About Provider Section */}
        <div className="flex flex-col w-full">
          <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
            {t("aboutProviderLabel")}
          </label>
          <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full min-h-[80px]">
            <p className="text-xs text-foreground font-['Poppins'] leading-relaxed">
              {service.provider.unsafe_metadata?.about_me || t("notAvailable")}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center items-center gap-12 px-5 py-2.5">
          <button
            onClick={handleDelete}
            className="w-[200px] h-[35px] bg-secondary rounded-[10px] flex items-center justify-center hover:bg-secondary/80 transition-colors"
          >
            <span className="text-xs font-['Poppins'] text-muted-foreground uppercase">
              {t("delete")}
            </span>
          </button>
          <button
            onClick={handleReject}
            className="w-[200px] h-[35px] bg-secondary rounded-[10px] flex items-center justify-center hover:bg-secondary/80 transition-colors"
          >
            <span className="text-xs font-['Poppins'] text-muted-foreground uppercase">
              {t("reject")}
            </span>
          </button>
          <button
            onClick={handleValidate}
            className="w-[200px] h-[35px] bg-secondary rounded-[10px] flex items-center justify-center hover:bg-secondary/80 transition-colors"
          >
            <span className="text-xs font-['Poppins'] text-muted-foreground uppercase">
              {t("validate")}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
