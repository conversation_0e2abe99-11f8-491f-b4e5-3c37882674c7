import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface TableDateFilterProps {
  table: {
    getColumn: (
      columnId: string,
    ) => NativeTableColumnInstance<Service> | undefined;
  };
}

export function TableDateFilter({ table }: TableDateFilterProps) {
  const t = useTranslations("Services");

  const dateColumn = table.getColumn("createdAt");

  return (
    <div>
      <Select
        onValueChange={(value) =>
          dateColumn?.setFilterValue(value === "all" ? "" : value)
        }
        value={(dateColumn?.getFilterValue() as string) || "all"}
      >
        <SelectTrigger className=" text-foreground w-full border-input rounded-md bg-background ">
          <SelectValue placeholder={t("dateThisYear")} />
          <ChevronDownIcon className="text-input" />{" "}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("dateAll")}</SelectItem>
          <SelectItem value="this-year">{t("thisYear")}</SelectItem>
          <SelectItem value="last-year">{t("lastYear")}</SelectItem>
          <SelectItem value="this-month">{t("thisMonth")}</SelectItem>
          <SelectItem value="last-month">{t("lastMonth")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
