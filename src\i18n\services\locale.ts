"use server";

import { cookies } from "next/headers";
import { Locale, defaultLocale } from "@/i18n/config";

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.
const COOKIE_NAME = "NEXT_LOCALE";

// Client-side state management
let currentLocale: Locale | null = null;

export async function getUserLocale() {
  if (currentLocale) return currentLocale;
  const cookieLocale = (await cookies()).get(COOKIE_NAME)?.value;
  currentLocale = (cookieLocale as Locale) || defaultLocale;
  return currentLocale;
}

// Cache for messages
type Messages = Record<string, Record<string, string>>;
const messageCache = new Map<Locale, Messages>();

export async function getMessages(locale: Locale): Promise<Messages> {
  if (messageCache.has(locale)) {
    return messageCache.get(locale)!;
  }
  const messages = (await import(`../../../messages/${locale}.json`)).default;
  messageCache.set(locale, messages);
  return messages;
}
