"use client";

import {
  Pa<PERSON><PERSON>,
  Pa<PERSON>ationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components";
import { useGlobalQueryParams } from "@/hooks";
import { useCallback, useMemo } from "react";
import { cn } from "@/lib";

interface GlobalPaginationProps extends React.ComponentProps<"nav"> {
  totalPages: number;
}

export function GlobalPagination(props: GlobalPaginationProps) {
  const {
    searchParams: { next },
    setSearchParams,
  } = useGlobalQueryParams();
  const { totalPages, className, ...rest } = props;

  const handlePageClick = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        // Change the page number here;
        setSearchParams((prev) => ({
          ...prev,
          next: page,
        }));
      }
    },
    [setSearchParams, totalPages],
  );

  const shouldShowPage = useCallback(
    (pageNumber: number, currentPage: number, total: number): boolean => {
      if (pageNumber === 1 || pageNumber === total) return true;
      if (total <= 5) return true;
      return pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1;
    },
    [],
  );

  const shouldShowEllipsis = useCallback(
    (pageNumber: number, currentPage: number, total: number): boolean => {
      if (total <= 5) return false;
      return (
        (pageNumber === 2 && currentPage > 3) ||
        (pageNumber === total - 1 && currentPage < total - 2)
      );
    },
    [],
  );

  const renderPageNumbers = useMemo(() => {
    const pages = [];

    for (let i = 1; i <= totalPages; i++) {
      if (shouldShowPage(i, next, totalPages)) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationLink
              href="#"
              isActive={i === next}
              onClick={() => handlePageClick(i)}
              className={cn(
                "w-9 h-9 text-sm border border-input text-main-black font-semibold",
                i === next && "bg-secondary border-secondary text-white",
              )}
            >
              {i}
            </PaginationLink>
          </PaginationItem>,
        );
      } else if (shouldShowEllipsis(i, next, totalPages)) {
        pages.push(
          <PaginationItem key={i}>
            <PaginationEllipsis />
          </PaginationItem>,
        );
      }
    }
    return pages;
  }, [next, handlePageClick, totalPages, shouldShowPage, shouldShowEllipsis]);

  return (
    <Pagination className={cn("flex justify-center", className)} {...rest}>
      <PaginationContent className="text-primary text-xs font-medium gap-2.5">
        {next > 1 && (
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={() => handlePageClick(next - 1)}
              className={cn(
                "w-9 h-9 text-sm border border-input text-main-black font-semibold",
              )}
            />
          </PaginationItem>
        )}
        {renderPageNumbers}
        {next < totalPages && (
          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={() => handlePageClick(next + 1)}
              className={cn(
                "w-9 h-9 text-sm border border-input text-main-black font-semibold",
              )}
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  );
}
