{"HomePage": {"title": "Hello World!", "about": "About the about page"}, "LocaleSwitcher": {"de": "German", "en": "English", "fr": "French", "code": "Code", "label": "Language"}, "FormsValidation": {"input_field_required": "Input field required", "select_field_required": "Select field required", "textarea_field_required": "Select field required", "username_required": "Username is required", "username_min_length": "Username must be at least 3 characters"}, "Auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "signInTitle": "Sign In - Back Office", "signInDescription": "Sign in to access your back office dashboard", "signUpTitle": "Sign Up - Back Office", "signUpDescription": "Create an account to access the back office system", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "rememberMe": "Remember me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "welcomeBack": "Welcome back!", "createYourAccount": "Create your account", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordReset": "Password reset email sent", "sessionExpired": "Your session has expired. Please sign in again.", "welcome_back": "Welcome Back", "email": "Email", "username": "Username", "password": "Password", "forgot_password": "Forgot Password?", "sign_in": "Sign In", "signing_in": "Signing in..."}, "Navigation": {"appName": "Application", "home": "Home", "about": "About", "services": "Services", "servicesList": "Services List", "servicesDetails": "Services Details", "support": "Support", "ticketsList": "Tickets List", "ticketsDetails": "Tickets Details", "settings": "Settings", "adminsList": "Administrators List", "adminsDetails": "Administrators Details", "newAdmin": "New Administrator", "contact": "Contact", "dashboard": "Dashboard", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "Header": {"language": "English, USD", "myAccount": "My Account", "signIn": "Sign In", "signOut": "Sign Out", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "Services": {"title": "Services", "subtitle": "Manage and view all services in the system", "pageDescription": "Showing {current} of {total} services", "name": "Name", "description": "Description", "provider": "Provider", "price": "Price", "status": "Status", "categories": "Categories", "published": "Published", "draft": "Draft", "noServices": "No services found", "noResults": "No results found", "filterByServiceName": "Filter by service name...", "filterByProvider": "Search by provider...", "filterByCategory": "Search by category...", "search": "Search...", "service": "Service", "dateThisYear": "This Year", "dateAll": "All Dates", "thisYear": "This Year", "lastYear": "Last Year", "thisMonth": "This Month", "lastMonth": "Last Month", "statusAll": "All Status", "statusFilterAll": "All", "statusPublished": "Published", "statusRefused": "Refused", "statusHidden": "Hidden", "statusDeleted": "Deleted", "statusPending": "Pending", "statusDisabled": "Disabled", "statusUnknown": "Unknown", "display10": "Display 10", "display20": "Display 20", "display50": "Display 50", "display100": "Display 100", "serviceId": "Service ID", "serviceTitle": "Service Title", "category": "Category", "userId": "User ID", "username": "Username", "createdAt": "Created At", "action": "Action", "viewMore": "View More"}, "ServiceDetails": {"title": "Service Details", "backToServices": "Back to Services", "serviceDetails": "Service Details", "error": "Error loading service", "serviceName": "Service Name", "serviceDescription": "Service Description", "shortDescription": "Short Description", "longDescription": "Long Description", "categories": "Categories", "noCategories": "No categories assigned", "locations": "Locations", "noLocations": "No locations specified", "serviceImages": "Service Images", "image": "Image", "noImages": "No images available", "imageLoadError": "Failed to load image", "aboutProviderLabel": "About Provider", "providerInformation": "Provider Information", "servicePopularity": "Service Popularity", "notAvailable": "Not available", "invalidDate": "Invalid date", "createdAt": "Created At", "statusLabel": "Status", "serviceIdLabel": "Service ID", "price": "Price", "pricingInformation": "Pricing Information", "country": "Country", "city": "City", "reject": "Reject", "delete": "Delete", "validate": "Validate", "cancel": "Cancel", "send": "Send", "rejectionReason": "Rejection Reason", "rejectionReasonLabel": "Rejection Reason*", "rejectionReasonPlaceholder": "Please indicate the reason for rejection that will be communicated to the user.", "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "rejected": "Rejected", "published": "Published", "draft": "Draft"}}}