import { useTranslations } from "next-intl";

interface ServiceCategoriesAndLocationsProps {
  service: Service;
}

export function ServiceCategoriesAndLocations({
  service,
}: ServiceCategoriesAndLocationsProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <>
      {/* Category */}
      <div className="flex flex-col w-full">
        <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
          {t("categories")} :
        </label>
        <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full">
          <span className="text-xs text-foreground font-['Poppins']">
            {service.categories && service.categories.length > 0
              ? service.categories.map((cat) => cat.name).join(", ")
              : t("noCategories")}
          </span>
        </div>
      </div>

      {/* Location */}
      <div className="flex flex-col w-full">
        <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
          {t("locations")} :
        </label>
        <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full">
          <span className="text-xs text-foreground font-['Poppins']">
            {service.locations && service.locations.length > 0
              ? service.locations.join(", ")
              : t("noLocations")}
          </span>
        </div>
      </div>
    </>
  );
}
