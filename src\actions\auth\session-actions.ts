"use server";

import { authService } from "@/services/auth";
import { CookieStorage } from "@/services/auth/token-storage";

/**
 * Session Management Actions
 * Focused on session validation and user data retrieval
 * Following Single Responsibility Principle from clean code practices
 */

/**
 * Server action to check if user is authenticated
 * Uses cookie storage for session validation
 */
export async function isUserAuthenticated(): Promise<boolean> {
  try {
    return await CookieStorage.isAuthenticated();
  } catch (error) {
    console.error("Authentication check error:", error);
    return false;
  }
}

/**
 * Server action to get current user profile
 * Uses cookie storage for session management
 */
export async function getCurrentUserSession(): Promise<ValidateTokenResponse> {
  try {
    // Get authentication data from cookies
    const authData = await CookieStorage.getAuthData();

    if (!authData.isAuthenticated || !authData.accessToken || !authData.user) {
      return {
        valid: false,
        error: {
          code: "NO_TOKEN",
          message: "No authentication token found",
        },
      };
    }

    // Validate token with API if needed
    const validationResult = await authService.validateToken(authData.accessToken);

    if (validationResult.valid) {
      return {
        valid: true,
        admin: authData.user,
      };
    }

    return validationResult;
  } catch (error) {
    console.error("Get current user session error:", error);
    return {
      valid: false,
      error: {
        code: "USER_FETCH_ERROR",
        message:
          error instanceof Error ? error.message : "Failed to get user data",
      },
    };
  }
}

/**
 * Server action to validate authentication token
 * Pure token validation without session management
 */
export async function validateAuthToken(
  token: string,
): Promise<ValidateTokenResponse> {
  try {
    const result = await authService.validateToken(token);
    return result;
  } catch (error) {
    console.error("Token validation error:", error);
    return {
      valid: false,
      error: {
        code: "TOKEN_VALIDATION_ERROR",
        message:
          error instanceof Error ? error.message : "Token validation failed",
      },
    };
  }
}

/**
 * Server action to get authentication data from cookies
 * Returns current session state without API calls
 */
export async function getSessionData(): Promise<{
  isAuthenticated: boolean;
  user: Admin | null;
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: string | null;
}> {
  try {
    const authData = await CookieStorage.getAuthData();
    return {
      isAuthenticated: authData.isAuthenticated,
      user: authData.user,
      accessToken: authData.accessToken,
      refreshToken: authData.refreshToken,
      expiresAt: authData.expiresAt,
    };
  } catch (error) {
    console.error("Get session data error:", error);
    return {
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
      expiresAt: null,
    };
  }
}

/**
 * Server action to update user data in session
 * Updates only user data without affecting tokens
 */
export async function updateUserSession(user: Admin): Promise<boolean> {
  try {
    await CookieStorage.updateUser(user);
    return true;
  } catch (error) {
    console.error("Update user session error:", error);
    return false;
  }
}

/**
 * Server action to clear session data
 * Clears all authentication cookies
 */
export async function clearUserSession(): Promise<boolean> {
  try {
    await CookieStorage.clear();
    return true;
  } catch (error) {
    console.error("Clear session error:", error);
    return false;
  }
}
