"use client";

import { Table } from "@/components/ui/table";
import { useNativeTable } from "@/hooks";
import { ServicesTableBody } from "./components/table-body";
import { DataTableHeader } from "./components/table-header";
import { TableFilters } from "./filters/table-filters";
import { TablePagination } from "./pagination/table-pagination";

interface ServicesDataTableProps {
  columns: NativeTableColumn<Service>[];
  data: Service[];
  totalItems: number;
  totalPages: number;
}

export function ServicesDataTable({
  columns,
  data,
  totalItems,
  totalPages,
}: ServicesDataTableProps) {
  // Utilisation de notre hook natif au lieu de TanStack
  const table = useNativeTable({ data, columns });

  // Note: Pagination is handled by the native table hook

  return (
    <div className="bg-white  rounded-[10px] border border-secondary overflow-hidden">
      <TableFilters table={table} />

      <Table>
        <DataTableHeader table={table} />
        <ServicesTableBody table={table} columns={columns} />
      </Table>

      <TablePagination totalItems={totalItems} totalPages={totalPages} />
    </div>
  );
}
