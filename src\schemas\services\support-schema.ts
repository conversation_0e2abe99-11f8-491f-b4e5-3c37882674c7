import { z } from "zod";
import { ProviderSchema } from "./provider-schema";

// Support Schema
export const SupportSchema = z.object({
  id: z.number(),
  name: z.string(),
  subject: z.string().optional(),
  message: z.string(),
  user: ProviderSchema.nullable(),
  status: z.string().optional(),
  priority: z.string().optional(),
  createdAt: z.string().nullable().optional(),
  updatedAt: z.string().nullable().optional(),
});

// Create Support Request Schema
export const CreateSupportRequestSchema = z.object({
  support: z.object({
    name: z.string().optional(),
    message: z.string(),
  }),
  extl_id: z.string().optional(),
});

// Edit Support Request Schema
export const EditSupportRequestSchema = z.object({
  support: z.object({
    id: z.number(),
    name: z.string(),
    message: z.string(),
  }),
});

// Support Parameters Schema
export const SupportParamsSchema = z.object({
  limit: z.number().optional(),
  next: z.number().optional(),
});

// Support Detail Parameters Schema
export const SupportDetailParamsSchema = z.object({
  id: z.string(),
});
