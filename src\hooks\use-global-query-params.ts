import { parseAsInteger, parseAsString, useQueryStates } from "nuqs";
// import { useMemo } from "react";

export function useGlobalQueryParams() {
  const [searchParams, setSearchParams] = useQueryStates(
    {
      limit: parseAsInteger.withDefault(10),
      next: parseAsInteger.withDefault(1),
      extl_id: parseAsString,
      currency: parseAsString.withDefault("EUR"),
      display: parseAsString,
    },
    {
      history: "push",
      shallow: false,
      clearOnDefault: false, // Keep parameters in URL even if they match defaults
    },
  );

  // const queryLength = useMemo(() => {
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  //   return count = 0;
  // }, []);

  return {
    searchParams,
    setSearchParams,
    // queryLength,
  };
}
