import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, CardContent } from "@/components/ui";
import { PricingDetails } from "./pricing-details";

interface PricingInformationProps {
  pricing: Pricing;
}

export function PricingInformation({ pricing }: PricingInformationProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <Card>
      <CardHeader>
        <h2 className="text-xl font-semibold">{t("pricingInformation")}</h2>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <PricingDetails pricing={pricing} />
        </div>
      </CardContent>
    </Card>
  );
}
