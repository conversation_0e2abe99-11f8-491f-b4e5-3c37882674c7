"use client";

import { ServicesDetailsContent } from "./services-details-content";
import { useRouter } from "next/navigation";

interface ServiceDetailsProps {
  service: Service;
}

/**
 * Sync component for service details presentation
 * Follows the established async/sync component separation pattern
 */
export function ServiceDetails({ service }: ServiceDetailsProps) {
  const router = useRouter();

  const handleBack = () => {
    router.push("/services", { scroll: false });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <ServicesDetailsContent service={service} onBack={handleBack} />
    </div>
  );
}
