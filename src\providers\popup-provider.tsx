"use client";

import { createContext, useContext, ReactNode } from "react";
import { usePopupManager } from "@/hooks/use-popup-manager";
import { ManagedPopup } from "@/components/common/managed-popup";

const PopupContext = createContext<PopupManagerActions | null>(null);

interface PopupProviderProps {
  children: ReactNode;
}

export function PopupProvider({ children }: PopupProviderProps) {
  const popupManager = usePopupManager();

  return (
    <PopupContext.Provider value={popupManager}>
      {children}
      <ManagedPopup
        isOpen={popupManager.isOpen}
        config={
          (popupManager as PopupManagerActions & { config?: PopupConfig })
            .config || null
        }
        onClose={popupManager.close}
      />
    </PopupContext.Provider>
  );
}

export function usePopup(): PopupManagerActions {
  const context = useContext(PopupContext);
  if (!context) {
    throw new Error("usePopup must be used within a PopupProvider");
  }
  return context;
}
