import {
  ServicesListResponseSchema,
  ServiceDetailResponseSchema,
} from "@/schemas/services";
import { BaseApiService } from "../base-service";
import { GlobalQueryParamsType } from "@/types/global";

export class ServicesServices extends BaseApiService {
  private readonly BASE_AUTH_URL = "services/";
  private readonly FILTERS_ENDPOINT = "all";
  private readonly DETAIL_ENDPOINT = "detail";

  constructor() {
    super();
    this.list = this.list.bind(this);
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.getByUserId = this.getByUserId.bind(this);
    this.validate = this.validate.bind(this);
  }

  /**
   * List all services with optional parameters
   * @param query - Global query parameters for filtering services
   * @returns Promise<ApiResponse<ServicesListResponse>>
   */
  async list(
    query: GlobalQueryParamsType,
  ): Promise<ApiResponse<ServicesListResponse>> {
    const { limit, next, ...rest } = query;

    return await this.request<ServicesListResponse>(
      `${this.BASE_AUTH_URL}${this.FILTERS_ENDPOINT}/${limit}/${next}`,
      {
        method: "GET",
        tag: `services-list-${limit}-${next}`,
        query: rest,
      },
      ServicesListResponseSchema,
    );
  }

  /**
   * Get a specific service by ID with optional query parameters
   * @param id - Service ID
   * @param query - Optional global query parameters
   * @returns Promise<ApiResponse<ServiceDetailResponse>>
   */
  async get(
    id: string,
    query?: GlobalQueryParamsType,
  ): Promise<ApiResponse<ServiceDetailResponse>> {
    return await this.request<ServiceDetailResponse>(
      `${this.BASE_AUTH_URL}${this.DETAIL_ENDPOINT}/${id}`,
      {
        method: "GET",
        tag: `service-detail-${id}`,
        query,
      },
      ServiceDetailResponseSchema,
    );
  }

  /**
   * Get services by user ID
   * @param params - User services parameters
   * @returns Promise<ApiResponse<UserServicesResponse>>
   */
  async getByUserId(
    _params: UserServicesParams,
  ): Promise<ApiResponse<UserServicesResponse>> {
    // TODO: Implement user services fetching
    throw new Error("Method not implemented yet");
  }

  /**
   * Create a new service
   * @param data - Service creation data
   * @returns Promise<ApiResponse<Service>>
   */
  async create(_data: CreateServiceRequest): Promise<ApiResponse<Service>> {
    // TODO: Implement service creation
    throw new Error("Method not implemented yet");
  }

  /**
   * Update an existing service
   * @param data - Service update data
   * @returns Promise<ApiResponse<Service>>
   */
  async update(_data: EditServiceRequest): Promise<ApiResponse<Service>> {
    // TODO: Implement service update
    throw new Error("Method not implemented yet");
  }

  /**
   * Delete a service
   * @param params - Delete service parameters
   * @returns Promise<ApiResponse<void>>
   */
  async delete(_params: DeleteServiceParams): Promise<ApiResponse<void>> {
    // TODO: Implement service deletion
    throw new Error("Method not implemented yet");
  }

  /**
   * Validate a service
   * @param params - Validation parameters
   * @returns Promise<ApiResponse<Service>>
   */
  async validate(
    _params: ValidateServiceParams,
  ): Promise<ApiResponse<Service>> {
    // TODO: Implement service validation
    throw new Error("Method not implemented yet");
  }
}
