declare type ApiResponse<T> = {
  status: number;
  response: T;
};

declare type ListApiResponse<T> = {
  nb_items: number;
  next_page: number;
  pagination: number;
  items: T[];
};

/*
NAVIGATIONS TYPES
TypeScript interfaces for navigation structure
*/
declare interface NavigationSubItem {
  titleKey: string;
  href: string;
  isActive?: boolean;
  isDetails?: boolean;
}

declare interface NavigationItem {
  titleKey: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  items?: NavigationSubItem[];
  isActive?: boolean;
  defaultOpen?: boolean;
}

declare interface NavigationData {
  navMain: NavigationItem[];
}

/*
NATIVE TABLE TYPES
TypeScript interfaces for the native React table implementation
*/

// Column definition for native table
declare interface NativeTableColumn<TData = unknown> {
  id: string;
  accessorKey?: keyof TData;
  header:
    | string
    | ((data: { column: NativeTableColumnInstance<TData> }) => React.ReactNode);
  cell?: (data: {
    row: NativeTableRow<TData>;
    value: unknown;
  }) => React.ReactNode;
  enableSorting?: boolean;
  enableFiltering?: boolean;
  filterFn?: (
    row: NativeTableRow<TData>,
    columnId: string,
    filterValue: unknown,
  ) => boolean;
  sortingFn?: (
    rowA: NativeTableRow<TData>,
    rowB: NativeTableRow<TData>,
  ) => number;
  size?: number;
  minSize?: number;
  maxSize?: number;
}

// Row data structure
declare interface NativeTableRow<TData = unknown> {
  id: string;
  original: TData;
  index: number;
  getValue: (columnId: string) => unknown;
}

// Column instance with state
declare interface NativeTableColumnInstance<TData = unknown>
  extends NativeTableColumn<TData> {
  getIsSorted: () => "asc" | "desc" | false;
  toggleSorting: (desc?: boolean) => void;
  getFilterValue: () => unknown;
  setFilterValue: (value: unknown) => void;
}

// Sorting state
declare interface NativeTableSortingState {
  id: string;
  desc: boolean;
}

// Filter state
declare interface NativeTableFilterState {
  id: string;
  value: unknown;
}

// Pagination state
declare interface NativeTablePaginationState {
  pageIndex: number;
  pageSize: number;
}

// Main table state
declare interface NativeTableState {
  sorting: NativeTableSortingState[];
  columnFilters: NativeTableFilterState[];
  pagination: NativeTablePaginationState;
  globalFilter: string;
}

// Table configuration
declare interface NativeTableConfig<TData = unknown> {
  data: TData[];
  columns: NativeTableColumn<TData>[];
  totalItems?: number;
  totalPages?: number;
  manualPagination?: boolean;
  manualSorting?: boolean;
  manualFiltering?: boolean;
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enableGlobalFilter?: boolean;
  initialState?: Partial<NativeTableState>;
}

// Table instance
declare interface NativeTableInstance<TData = unknown> {
  // Data
  getRowModel: () => { rows: NativeTableRow<TData>[] };
  getFilteredRowModel: () => { rows: NativeTableRow<TData>[] };
  getSortedRowModel: () => { rows: NativeTableRow<TData>[] };

  // Columns
  getAllColumns: () => NativeTableColumnInstance<TData>[];
  getColumn: (columnId: string) => NativeTableColumnInstance<TData> | undefined;

  // State
  getState: () => NativeTableState<TData>;

  // Pagination
  getPageCount: () => number;
  getCanPreviousPage: () => boolean;
  getCanNextPage: () => boolean;
  previousPage: () => void;
  nextPage: () => void;
  setPageIndex: (pageIndex: number) => void;
  setPageSize: (pageSize: number) => void;

  // Sorting
  setSorting: (
    sorting:
      | NativeTableSortingState[]
      | ((prev: NativeTableSortingState[]) => NativeTableSortingState[]),
  ) => void;

  // Filtering
  setColumnFilters: (
    filters:
      | NativeTableFilterState[]
      | ((prev: NativeTableFilterState[]) => NativeTableFilterState[]),
  ) => void;
  setGlobalFilter: (filter: string) => void;

  // Reset
  reset: () => void;
}

/*
POPUP MANAGER TYPES
TypeScript interfaces for the popup manager system
*/

declare interface PopupInputConfig {
  placeholder: string;
  required?: boolean;
  validation?: (value: string) => boolean;
  rows?: number;
  maxLength?: number;
}

declare interface PopupActionConfig {
  label: string;
  onClick: (inputValue?: string) => void;
  disabled?: boolean;
  variant?: "default" | "outline" | "destructive";
}

declare interface PopupConfig {
  title: string;
  description?: string;
  input?: PopupInputConfig;
  actions: {
    cancel: PopupActionConfig;
    confirm: PopupActionConfig;
  };
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
  onOpen?: () => void;
  onClose?: () => void;
}

declare interface PopupManagerState {
  isOpen: boolean;
  config: PopupConfig | null;
}

declare interface PopupManagerActions {
  create: (config: PopupConfig) => void;
  close: () => void;
  isOpen: boolean;
}
