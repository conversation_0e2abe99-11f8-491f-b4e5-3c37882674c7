"use client";

import { useRef, useEffect, useState } from "react";

interface TruncatedTextProps {
  text: string;
  maxWidth?: string;
}

export function TruncatedText({ text, maxWidth = "200" }: TruncatedTextProps) {
  const textRef = useRef<HTMLSpanElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    if (textRef.current) {
      setIsOverflowing(
        textRef.current.scrollWidth > textRef.current.clientWidth,
      );
    }
  }, [text]);

  return (
    <span
      className={`relative ${isOverflowing ? "cursor-pointer" : "cursor-default"} group inline-block`}
    >
      <span
        ref={textRef}
        style={{ maxWidth: `${maxWidth}px` }}
        className="truncate inline-block"
      >
        {text}
      </span>
      {isOverflowing && (
        <div className="absolute -left-5 -top-1.5 hidden group-hover:block bg-popover text-popover-foreground z-[100] rounded-md border border-input p-1 shadow-lg whitespace-nowrap">
          {text}
        </div>
      )}
    </span>
  );
}
