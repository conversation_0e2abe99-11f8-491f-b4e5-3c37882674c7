import { useTranslations } from "next-intl";

interface TableStatusBadgeProps {
  status: string;
}

interface StatusConfig {
  label: string;
  className: string;
}

// Type for translation function - more flexible to work with useTranslations
type TranslationFunction = (key: string) => string;

// Helper functions to reduce complexity
const getPublishedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusPublished"),
  className: "bg-green-100 text-green-800",
});

const getRefusedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusRefused"),
  className: "bg-red-100 text-red-800",
});

const getHiddenConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusHidden"),
  className: "bg-gray-100 text-gray-800",
});

const getDeletedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusDeleted"),
  className: "bg-red-100 text-red-800",
});

const getPendingConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusPending"),
  className: "bg-yellow-100 text-yellow-800",
});

const getDisabledConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusDisabled"),
  className: "bg-gray-100 text-gray-800",
});

const getUnknownConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusUnknown"),
  className: "bg-gray-100 text-gray-800",
});

// Status mapping to reduce complexity
const STATUS_MAPPING: Record<string, (t: TranslationFunction) => StatusConfig> =
  {
    published: getPublishedConfig,
    publié: getPublishedConfig,
    refused: getRefusedConfig,
    refusé: getRefusedConfig,
    hidden: getHiddenConfig,
    masqué: getHiddenConfig,
    deleted: getDeletedConfig,
    supprimé: getDeletedConfig,
    pending: getPendingConfig,
    "en attente": getPendingConfig,
    disabled: getDisabledConfig,
    désactivé: getDisabledConfig,
  };

const getStatusConfig = (
  status: string,
  t: TranslationFunction,
): StatusConfig => {
  const normalizedStatus = status.toLowerCase();
  const configFunction = STATUS_MAPPING[normalizedStatus];

  return configFunction ? configFunction(t) : getUnknownConfig(t);
};

export function TableStatusBadge({ status }: TableStatusBadgeProps) {
  const t = useTranslations("Services");

  // Create a wrapper function that matches our TranslationFunction type
  // We use type assertion to work around the strict typing of useTranslations
  const translationWrapper: TranslationFunction = (key: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (t as any)(key);
  };

  const config = getStatusConfig(status, translationWrapper);

  return (
    <span
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${config.className}`}
    >
      {config.label}
    </span>
  );
}
