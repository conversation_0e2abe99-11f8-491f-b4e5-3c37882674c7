"use client";

import { TableCell } from "@/components/ui/table";

interface TableBodyCellProps<TData = unknown> {
  column: NativeTableColumnInstance<TData>;
  row: NativeTableRow<TData>;
}

export function TableBodyCell<TData = unknown>({
  column,
  row,
}: TableBodyCellProps<TData>) {
  const value = row.getValue(column.id);

  const renderCell = () => {
    if (column.cell) {
      return column.cell({ row, value });
    }

    // Rendu par défaut
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">-</span>;
    }

    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    if (Array.isArray(value)) {
      return value.join(", ");
    }

    if (typeof value === "object") {
      return JSON.stringify(value);
    }

    return String(value);
  };

  return (
    <TableCell
      style={{
        width: column.size && column.size > 0 ? `${column.size}px` : undefined,
        minWidth: column.minSize ? `${column.minSize}px` : undefined,
        maxWidth: column.maxSize ? `${column.maxSize}px` : undefined,
      }}
    >
      {renderCell()}
    </TableCell>
  );
}
