"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface ManagedPopupProps {
  isOpen: boolean;
  config: PopupConfig | null;
  onClose: () => void;
}

// Size mapping for responsive design
const SIZE_CLASSES = {
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  "2xl": "max-w-2xl",
  "3xl": "max-w-3xl",
  "4xl": "max-w-4xl",
  "5xl": "max-w-5xl",
} as const;

// Helper functions to reduce complexity
const logPopupAction = (action: string, title: string, inputValue?: string) => {
  console.log(`Popup ${action}:`, {
    title,
    ...(inputValue !== undefined && { inputValue }),
    ...(action === "input changed" && { valueLength: inputValue?.length }),
    timestamp: new Date().toISOString(),
  });
};

const validateInput = (
  config: PopupConfig,
  inputValue: string,
): string | null => {
  if (config.input?.required && !inputValue.trim()) {
    return "This field is required";
  }

  if (config.input?.validation && !config.input.validation(inputValue)) {
    return "Invalid input";
  }

  return null;
};

const isConfirmButtonDisabled = (
  config: PopupConfig,
  inputValue: string,
): boolean => {
  // Check if confirm button is explicitly disabled
  if (config.actions.confirm.disabled) {
    return true;
  }

  // Check if required input is empty
  if (config.input?.required && !inputValue.trim()) {
    return true;
  }

  // Check if validation fails
  if (config.input?.validation && !config.input.validation(inputValue)) {
    return true;
  }

  return false;
};

export function ManagedPopup({ isOpen, config, onClose }: ManagedPopupProps) {
  const [inputValue, setInputValue] = useState("");
  const [validationError, setValidationError] = useState<string | null>(null);

  // Reset input value when popup opens/closes
  useEffect(() => {
    if (isOpen) {
      setInputValue("");
      setValidationError(null);
    }
  }, [isOpen]);

  if (!config) return null;

  const sizeClass = SIZE_CLASSES[config.size || "5xl"];

  const handleInputChange = (value: string) => {
    logPopupAction("input changed", config.title, value);
    setInputValue(value);
    setValidationError(null);

    // Run validation if provided
    if (config.input?.validation && !config.input.validation(value)) {
      setValidationError("Invalid input");
    }
  };

  const handleCancel = () => {
    logPopupAction("cancelled", config.title, inputValue);
    config.actions.cancel.onClick();
    onClose();
  };

  const handleConfirm = () => {
    const error = validateInput(config, inputValue);
    if (error) {
      setValidationError(error);
      return;
    }

    logPopupAction("confirmed", config.title, inputValue);
    config.actions.confirm.onClick(inputValue);
    onClose();
  };

  const isConfirmDisabled = isConfirmButtonDisabled(config, inputValue);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`bg-background p-0 gap-0 w-full ${sizeClass} min-w-0 !rounded-none border-0 shadow-none overflow-hidden`}
        showCloseButton={false}
      >
        {/* Header with close button - matching Figma design exactly */}
        <DialogHeader className="absolute inset-x-0 top-0 flex flex-row justify-between items-center gap-14 px-5 py-2 border-b border-border h-15 bg-background z-10">
          <div className="size-6" /> {/* Spacer for alignment */}
          <DialogTitle className="text-base font-medium text-foreground leading-tight">
            {config.title}
          </DialogTitle>
          <button
            onClick={handleCancel}
            className="size-6 flex items-center justify-center hover:bg-muted rounded transition-colors"
          >
            <X className="size-3.5 text-foreground" />
          </button>
        </DialogHeader>

        {/* Content - responsive padding and spacing */}
        <div className="flex flex-col gap-6 pt-20 px-5 pb-5 w-full max-w-full overflow-hidden">
          {/* Description */}
          {config.description && (
            <DialogDescription className="text-sm text-foreground">
              {config.description}
            </DialogDescription>
          )}

          {/* Input field with label - responsive design */}
          {config.input && (
            <div className="flex flex-col w-full space-y-2 max-w-full overflow-hidden">
              <label className="text-xs font-normal text-foreground leading-relaxed flex items-center min-h-8">
                {config.input.placeholder.includes("*")
                  ? config.input.placeholder
                  : `${config.input.placeholder}*`}{" "}
                :
              </label>
              <div className="flex-1 max-w-full overflow-hidden">
                <Textarea
                  value={inputValue}
                  onChange={(e) => handleInputChange(e.target.value)}
                  placeholder="Merci d'indiquer la raison du refus qui sera communiquée à l'utilisateur."
                  className="w-full min-h-20 bg-background border border-border rounded-[var(--radius-lg px-3 py-2 text-xs font-normal text-foreground placeholder:text-muted-foreground leading-relaxed resize-none break-words whitespace-pre-wrap overflow-wrap-anywhere focus:ring-2 focus:ring-ring focus:border-transparent"
                  rows={config.input.rows || 3}
                  maxLength={config.input.maxLength}
                />
                {validationError && (
                  <p className="text-xs text-destructive mt-1">
                    {validationError}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Action buttons - responsive design with proper spacing */}
          <div className="flex flex-row justify-center items-center gap-12 px-5 py-3 w-full">
            <Button
              onClick={handleCancel}
              variant="ghost"
              disabled={config.actions.cancel.disabled}
              className="w-48 h-9 bg-secondary border-0 rounded-[var(--radius-lg px-5 py-0 text-xs font-normal text-muted-foreground uppercase leading-relaxed hover:bg-accent transition-colors flex items-center justify-center"
            >
              {config.actions.cancel.label}
            </Button>
            <Button
              onClick={handleConfirm}
              variant="ghost"
              disabled={isConfirmDisabled}
              className="w-48 h-9 bg-secondary border-0 rounded-[var(--radius-lg px-5 py-0 text-xs font-normal text-muted-foreground uppercase leading-relaxed hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {config.actions.confirm.label}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
