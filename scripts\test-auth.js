/**
 * Simple Authentication Test Script
 * 
 * This script tests the basic authentication flow without persistence or RBAC.
 * Use this to verify the simplified authentication system works correctly.
 * 
 * Usage: node scripts/test-auth.js
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

/**
 * Test authentication with the simplified system
 */
async function testAuthentication() {
  console.log('🔐 Testing Simplified Authentication System');
  console.log('==========================================');
  
  try {
    // Test login
    console.log('\n1. Testing Login...');
    const loginResponse = await fetch(`${API_BASE_URL}/admin/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'your-password-here', // Replace with actual password
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log('📋 Response structure:', {
      hasAccessToken: !!loginData.accessToken,
      hasRefreshToken: !!loginData.refreshToken,
      hasAdmin: !!loginData.admin,
      adminEmail: loginData.admin?.email,
    });

    // Test token validation
    console.log('\n2. Testing Token Validation...');
    const validateResponse = await fetch(`${API_BASE_URL}/admin/auth/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!validateResponse.ok) {
      throw new Error(`Token validation failed: ${validateResponse.status} ${validateResponse.statusText}`);
    }

    const profileData = await validateResponse.json();
    console.log('✅ Token validation successful');
    console.log('👤 Profile data:', {
      id: profileData.id,
      email: profileData.email,
      firstName: profileData.firstName,
      lastName: profileData.lastName,
    });

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📝 Notes:');
    console.log('- Authentication is working correctly');
    console.log('- No token persistence (session-only)');
    console.log('- No RBAC verification implemented');
    console.log('- Ready for frontend integration');

  } catch (error) {
    console.error('\n❌ Authentication test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check if the API server is running');
    console.log('2. Verify the API_BASE_URL is correct');
    console.log('3. Ensure the test credentials are valid');
    console.log('4. Check network connectivity');
  }
}

/**
 * Test the authentication endpoints availability
 */
async function testEndpointsAvailability() {
  console.log('\n🌐 Testing API Endpoints Availability');
  console.log('====================================');

  const endpoints = [
    { name: 'Login', path: '/admin/auth/login', method: 'POST' },
    { name: 'Profile', path: '/admin/auth/profile', method: 'GET' },
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint.path}`, {
        method: 'OPTIONS', // Use OPTIONS to check if endpoint exists
      });
      
      console.log(`✅ ${endpoint.name} endpoint (${endpoint.method} ${endpoint.path}): Available`);
    } catch (error) {
      console.log(`❌ ${endpoint.name} endpoint (${endpoint.method} ${endpoint.path}): Not available`);
    }
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Authentication System Tests\n');
  
  await testEndpointsAvailability();
  await testAuthentication();
  
  console.log('\n✨ Test completed!');
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testAuthentication,
  testEndpointsAvailability,
  runTests,
};
