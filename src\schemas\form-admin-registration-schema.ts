import { z } from "zod";

/**
 * Admin registration form validation schema
 * Used for creating new admin accounts (super admin only)
 * Following clean code principles with consistent naming and error messages
 */

// ============================================================================
// ERROR MESSAGE CONSTANTS
// ============================================================================

/**
 * Standardized error message patterns for registration
 * Consistent with login schema format
 */
const ERROR_MESSAGES = {
  EMAIL_REQUIRED: "email_required",
  EMAIL_INVALID_FORMAT: "email_invalid_format",
  EMAIL_TOO_LONG: "email_too_long",
  FIRST_NAME_REQUIRED: "first_name_required",
  FIRST_NAME_TOO_LONG: "first_name_too_long",
  LAST_NAME_REQUIRED: "last_name_required",
  LAST_NAME_TOO_LONG: "last_name_too_long",
  PASSWORD_REQUIRED: "password_required",
  PASSWORD_MIN_LENGTH: "password_min_length_8",
  PASSWORD_TOO_LONG: "password_too_long",
  PASSWORD_COMPLEXITY: "password_complexity_requirements",
} as const;

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

/**
 * Validation constraints for admin registration
 * More strict requirements for admin accounts
 */
const VALIDATION_RULES = {
  EMAIL_MAX_LENGTH: 255,
  FIRST_NAME_MAX_LENGTH: 50,
  LAST_NAME_MAX_LENGTH: 50,
  PASSWORD_MIN_LENGTH: 8, // Stricter for admin accounts
  PASSWORD_MAX_LENGTH: 100,
} as const;

/**
 * Password complexity regex
 * Requires: uppercase, lowercase, number, and special character
 * Expanded special characters for better usability
 */
const PASSWORD_COMPLEXITY_REGEX =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#^(){}[\]|;:,.<>?/~`\-_+=])[A-Za-z\d@$!%*?&#^(){}[\]|;:,.<>?/~`\-_+=]+$/;

// ============================================================================
// SCHEMA DEFINITION
// ============================================================================

/**
 * Admin registration form schema
 * Matches AdminCreateDTO interface from API specification
 */
export const FormAdminRegistrationSchema = z.object({
  email: z
    .string({ required_error: ERROR_MESSAGES.EMAIL_REQUIRED })
    .min(1, { message: ERROR_MESSAGES.EMAIL_REQUIRED })
    .email({ message: ERROR_MESSAGES.EMAIL_INVALID_FORMAT })
    .max(VALIDATION_RULES.EMAIL_MAX_LENGTH, {
      message: ERROR_MESSAGES.EMAIL_TOO_LONG,
    }),

  firstName: z
    .string({ required_error: ERROR_MESSAGES.FIRST_NAME_REQUIRED })
    .min(1, { message: ERROR_MESSAGES.FIRST_NAME_REQUIRED })
    .max(VALIDATION_RULES.FIRST_NAME_MAX_LENGTH, {
      message: ERROR_MESSAGES.FIRST_NAME_TOO_LONG,
    }),

  lastName: z
    .string({ required_error: ERROR_MESSAGES.LAST_NAME_REQUIRED })
    .min(1, { message: ERROR_MESSAGES.LAST_NAME_REQUIRED })
    .max(VALIDATION_RULES.LAST_NAME_MAX_LENGTH, {
      message: ERROR_MESSAGES.LAST_NAME_TOO_LONG,
    }),

  password: z
    .string({ required_error: ERROR_MESSAGES.PASSWORD_REQUIRED })
    .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, {
      message: ERROR_MESSAGES.PASSWORD_MIN_LENGTH,
    })
    .max(VALIDATION_RULES.PASSWORD_MAX_LENGTH, {
      message: ERROR_MESSAGES.PASSWORD_TOO_LONG,
    })
    .regex(PASSWORD_COMPLEXITY_REGEX, {
      message: ERROR_MESSAGES.PASSWORD_COMPLEXITY,
    }),
});

/**
 * Inferred type from admin registration schema
 */
export type FormAdminRegistrationData = z.infer<
  typeof FormAdminRegistrationSchema
>;

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validate email format for registration
 */
export const validateRegistrationEmail = (email: string): boolean => {
  return FormAdminRegistrationSchema.shape.email.safeParse(email).success;
};

/**
 * Validate first name requirements
 */
export const validateFirstName = (firstName: string): boolean => {
  return FormAdminRegistrationSchema.shape.firstName.safeParse(firstName)
    .success;
};

/**
 * Validate last name requirements
 */
export const validateLastName = (lastName: string): boolean => {
  return FormAdminRegistrationSchema.shape.lastName.safeParse(lastName).success;
};

/**
 * Validate password complexity for admin accounts
 */
export const validateRegistrationPassword = (password: string): boolean => {
  return FormAdminRegistrationSchema.shape.password.safeParse(password).success;
};

/**
 * Check password complexity requirements individually
 * Useful for providing specific feedback to users
 */
export const checkPasswordComplexity = (password: string) => {
  const hasMinLength = password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH;
  const hasMaxLength = password.length <= VALIDATION_RULES.PASSWORD_MAX_LENGTH;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[@$!%*?&#^(){}[\]|;:,.<>?/~`\-_+=]/.test(password);

  return {
    hasMinLength,
    hasMaxLength,
    hasUppercase,
    hasLowercase,
    hasNumber,
    hasSpecialChar,
    isValid:
      hasMinLength &&
      hasMaxLength &&
      hasUppercase &&
      hasLowercase &&
      hasNumber &&
      hasSpecialChar,
  };
};
