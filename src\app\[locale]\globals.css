@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-5: 5px;
  --radius-10: 10px;
  --radius-15: 12px;
  --radius-30: 30px;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-main-black: var(--main-black);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  /* Base colors from Figma design */
  --background: #ffffff;
  --foreground: #373737;
  --radius: 0.625rem;

  /* Card components */
  --card: #ffffff;
  --card-foreground: #373737;

  /* Popover components */
  --popover: #ffffff;
  --popover-foreground: #373737;

  /* Primary brand color - Blue from Figma */
  --primary: #29728e;
  --primary-foreground: #ffffff;

  /* Secondary colors - Light gray backgrounds */
  --secondary: #ededed;
  --secondary-foreground: #373737;

  /* Muted colors for subtle elements */
  --muted: #ededed;
  --muted-foreground: rgba(55, 55, 55, 0.5);

  /* Accent colors */
  --accent: #ededed;
  --accent-foreground: #373737;

  /* Destructive/Error colors */
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;

  /* Border and input styling */
  --border: #ededed;
  --input: #c2c2c2;
  --ring: #29728e;
  --select: #c2c2c2;

  /* Chart colors from Figma palette */
  --chart-1: #29728e;
  --chart-2: #d4a01f;
  --chart-3: #10922c;
  --chart-4: #ff0000;
  --chart-5: #d9d9d9;

  /* Sidebar colors */
  --sidebar: #ffffff;
  --sidebar-foreground: #373737;
  --sidebar-primary: #29728e;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #ededed;
  --sidebar-accent-foreground: #373737;
  --sidebar-border: #ededed;
  --sidebar-ring: #29728e;

  /* Success state */
  --success: #10922c;
  --success-foreground: #ffffff;

  /* Warning state */
  --warning: #d4a01f;
  --warning-foreground: #ffffff;

  /* Table specific colors */
  --table-header: #ededed;
  --table-header-foreground: #373737;
  --table-row-even: #ffffff;
  --table-row-odd: #fafafa;
  --table-border: #ededed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Sidebar colors */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);

  /* Chart colors */
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);

  /* Form and interaction colors */
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);

  /* State colors */
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);

  /* Component colors */
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);

  /* Table colors */
  --color-table-header: var(--table-header);
  --color-table-header-foreground: var(--table-header-foreground);
  --color-table-row-even: var(--table-row-even);
  --color-table-row-odd: var(--table-row-odd);
  --color-table-border: var(--table-border);

  /* Border radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

body {
  font-family:
    "Poppins",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}
