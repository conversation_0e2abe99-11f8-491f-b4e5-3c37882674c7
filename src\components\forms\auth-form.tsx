import { zodResolver } from "@hookform/resolvers/zod";

import { FormLoginData, FormLoginSchema } from "@/schemas";
import React, { useOptimistic, useTransition } from "react";
import { useForm } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Button, Form } from "../ui";
import { InputField } from "./form-fields/input-field";
import { IconLock, IconMail } from "@tabler/icons-react";
import { Link } from "@/i18n";
import { login } from "@/actions/auth-actions";

type AuthState = {
  isLoading: boolean;
};

export function AuthForm() {
  const t = useTranslations("Auth");
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Optimistic state for better UX
  const [authState, setOptimisticAuthState] = useOptimistic({
    isLoading: false,
  } as AuthState);

  const form = useForm<FormLoginData>({
    resolver: zodResolver(FormLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormLoginData) => {
    startTransition(async () => {
      // Optimistically set loading state inside transition
      setOptimisticAuthState({ isLoading: true });

      try {
        const result = await login(data);

        if (result.success && result.data) {
          // Show success toast
          toast.success(result.data.message);

          // Set loading to false
          setOptimisticAuthState({ isLoading: false });

          // Get redirect URL from search params or default to root
          const redirectTo = searchParams.get("redirect") || "/";
          router.push(redirectTo);
        } else {
          // Show error toast
          toast.error(
            result.error?.message || "Login failed. Please try again.",
          );

          // Set loading to false
          setOptimisticAuthState({ isLoading: false });
        }
      } catch (_err) {
        // Show error toast for unexpected errors
        toast.error("An unexpected error occurred. Please try again.");

        // Set loading to false
        setOptimisticAuthState({ isLoading: false });
      }
    });
  };

  const isLoading = authState.isLoading || isPending;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Email Field */}
        <InputField
          control={form.control}
          fieldName="email"
          label={t("email")}
          type="email"
          required
          disabled={isLoading}
          icon={{
            icon: <IconMail className="h-5 w-5" />,
            position: "start",
          }}
        />

        {/* Password Field */}
        <InputField
          control={form.control}
          fieldName="password"
          label={t("password")}
          type="password"
          required
          disabled={isLoading}
          icon={{
            icon: <IconLock className="h-5 w-5" />,
            position: "start",
          }}
        />

        {/* Forgot Password Link */}
        <div className="text-right">
          <Link
            href="/forgot-password"
            className="text-sm text-primary hover:text-primary/80 transition-colors"
          >
            {t("forgot_password")}
          </Link>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground font-semibold transition-all duration-200"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>{t("signing_in")}</span>
            </div>
          ) : (
            t("sign_in")
          )}
        </Button>
      </form>
    </Form>
  );
}
