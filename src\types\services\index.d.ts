import {
  // Service schemas
  CategorySchema,
  CreateServiceRequestSchema,
  CreateSupportRequestSchema,
  DeleteServiceParamsSchema,
  EditServiceRequestSchema,
  EditSupportRequestSchema,
  // Pagination schemas
  PaginatedResponseSchema,
  PaginationParamsSchema,
  PricingSchema,
  ProviderAddressSchema,
  // Provider schemas
  ProviderContactSchema,
  ProviderSchema,
  ProviderUnsafeMetadataSchema,
  ServiceBaseSchema,
  ServiceDetailParamsSchema,
  ServiceDetailResponseSchema,
  ServicePricingRequestSchema,
  ServicesAllParamsSchema,
  ServiceSchema,
  ServicesFiltersParamsSchema,
  // Response schemas
  ServicesListResponseSchema,
  SupportDetailParamsSchema,
  SupportDetailResponseSchema,
  SupportParamsSchema,
  // Support schemas
  SupportSchema,
  SupportsListResponseSchema,
  UserServicesParamsSchema,
  UserServicesResponseSchema,
  ValidateServiceParamsSchema,
} from "@/schemas/services";
import { z } from "zod";

// Export empty to make this a module
export {};

declare global {
  // Provider types
  type ProviderContact = z.infer<typeof ProviderContactSchema>;
  type ProviderAddress = z.infer<typeof ProviderAddressSchema>;
  type ProviderUnsafeMetadata = z.infer<typeof ProviderUnsafeMetadataSchema>;
  type Provider = z.infer<typeof ProviderSchema>;

  // Service types
  type Category = z.infer<typeof CategorySchema>;
  type Pricing = z.infer<typeof PricingSchema>;
  type ServiceBase = z.infer<typeof ServiceBaseSchema>;
  type Service = z.infer<typeof ServiceSchema>;
  type ServicePricingRequest = z.infer<typeof ServicePricingRequestSchema>;

  // Request types
  type CreateServiceRequest = z.infer<typeof CreateServiceRequestSchema>;
  type EditServiceRequest = z.infer<typeof EditServiceRequestSchema>;
  type ValidateServiceParams = z.infer<typeof ValidateServiceParamsSchema>;
  type DeleteServiceParams = z.infer<typeof DeleteServiceParamsSchema>;

  // Pagination types
  type PaginatedResponse = z.infer<typeof PaginatedResponseSchema>;
  type PaginationParams = z.infer<typeof PaginationParamsSchema>;
  type ServicesAllParams = z.infer<typeof ServicesAllParamsSchema>;
  type ServicesFiltersParams = z.infer<typeof ServicesFiltersParamsSchema>;
  type ServiceDetailParams = z.infer<typeof ServiceDetailParamsSchema>;
  type UserServicesParams = z.infer<typeof UserServicesParamsSchema>;

  // Support types
  type Support = z.infer<typeof SupportSchema>;
  type CreateSupportRequest = z.infer<typeof CreateSupportRequestSchema>;
  type EditSupportRequest = z.infer<typeof EditSupportRequestSchema>;
  type SupportParams = z.infer<typeof SupportParamsSchema>;
  type SupportDetailParams = z.infer<typeof SupportDetailParamsSchema>;

  // Response types
  type ServicesListResponse = z.infer<typeof ServicesListResponseSchema>;
  type ServiceDetailResponse = z.infer<typeof ServiceDetailResponseSchema>;
  type UserServicesResponse = z.infer<typeof UserServicesResponseSchema>;
  type SupportDetailResponse = z.infer<typeof SupportDetailResponseSchema>;
  type SupportsListResponse = z.infer<typeof SupportsListResponseSchema>;
}
