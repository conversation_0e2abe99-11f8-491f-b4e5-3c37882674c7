import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface TableStatusFilterProps {
  table: {
    getColumn: (
      columnId: string,
    ) => NativeTableColumnInstance<Service> | undefined;
  };
}

export function TableStatusFilter({ table }: TableStatusFilterProps) {
  const t = useTranslations("Services");

  const statusColumn = table.getColumn("status");

  return (
    <div>
      <Select
        onValueChange={(value) =>
          statusColumn?.setFilterValue(value === "all" ? "" : value)
        }
        value={(statusColumn?.getFilterValue() as string) || "all"}
      >
        <SelectTrigger className="w-full border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:ring-offset-2">
          <SelectValue placeholder={t("statusAll")} />
          <ChevronDownIcon className="text-input" />{" "}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("statusFilterAll")}</SelectItem>
          <SelectItem value="published">{t("statusPublished")}</SelectItem>
          <SelectItem value="refused">{t("statusRefused")}</SelectItem>
          <SelectItem value="hidden">{t("statusHidden")}</SelectItem>
          <SelectItem value="deleted">{t("statusDeleted")}</SelectItem>
          <SelectItem value="pending">{t("statusPending")}</SelectItem>
          <SelectItem value="disabled">{t("statusDisabled")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
