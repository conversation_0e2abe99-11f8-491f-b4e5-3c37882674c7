import { getServiceDetailAction } from "@/actions";
import { getGlobalQueryParams } from "@/lib";
import { notFound } from "next/navigation";
import { ServiceDetails } from "./service-details";
import { SearchParams } from "nuqs";
import { GlobalQueryParamsType } from "@/types/global";

interface AsyncServiceDetailsProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

/**
 * Async component for server-side service detail data fetching
 * Follows the established async/sync component separation pattern
 */
export async function AsyncServiceDetails({
  params,
  searchParams,
}: AsyncServiceDetailsProps) {
  const { id } = await params;
  const query = await getGlobalQueryParams(searchParams);

  try {
    const response = await getServiceDetailAction(
      id,
      query as unknown as GlobalQueryParamsType,
    );

    if (!response.response || response.status === 404) {
      return notFound();
    }

    return <ServiceDetails service={response.response} />;
  } catch (error) {
    throw error; // Let error boundary handle it
  }
}
