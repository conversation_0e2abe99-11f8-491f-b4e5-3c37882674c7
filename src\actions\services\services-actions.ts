"use server";

import { ServicesServices } from "@/services";
import { GlobalQueryParamsType } from "@/types/global";

/**
 * List services with query parameters
 * @param query - Global query parameters for filtering services
 * @returns Promise<ApiResponse<ServicesListResponse>>
 */
export async function listServicesAction(query: GlobalQueryParamsType) {
  const { list } = new ServicesServices();
  return await list(query);
}

/**
 * Get service detail by ID
 * @param id - Service ID
 * @param query - Optional global query parameters
 * @returns Promise<ApiResponse<ServiceDetailResponse>>
 */
export async function getServiceDetailAction(
  id: string,
  query?: GlobalQueryParamsType,
) {
  const { get } = new ServicesServices();
  return await get(id, query);
}

/**
 * Get services by user ID
 * @param params - User services parameters
 * @returns Promise<ApiResponse<UserServicesResponse>>
 */
export async function getServicesByUserIdAction(params: UserServicesParams) {
  const { getByUserId } = new ServicesServices();
  return await getByUserId(params);
}

/**
 * Create a new service
 * @param data - Service creation data
 * @returns Promise<ApiResponse<Service>>
 */
export async function createServiceAction(data: CreateServiceRequest) {
  const { create } = new ServicesServices();
  return await create(data);
}

/**
 * Update an existing service
 * @param data - Service update data
 * @returns Promise<ApiResponse<Service>>
 */
export async function updateServiceAction(data: EditServiceRequest) {
  const { update } = new ServicesServices();
  return await update(data);
}

/**
 * Delete a service
 * @param params - Delete service parameters
 * @returns Promise<ApiResponse<void>>
 */
export async function deleteServiceAction(params: DeleteServiceParams) {
  const { delete: deleteService } = new ServicesServices();
  return await deleteService(params);
}

/**
 * Validate a service
 * @param params - Validation parameters
 * @returns Promise<ApiResponse<Service>>
 */
export async function validateServiceAction(params: ValidateServiceParams) {
  const { validate } = new ServicesServices();
  return await validate(params);
}
