import { <PERSON><PERSON><PERSON>, TableHeader, TableRow } from "@/components/ui/table";
import { TableHeaderCell } from "@/components/common/table/table-header-cell";

interface ServicesTableHeaderProps {
  table: {
    columns: NativeTableColumnInstance<Service>[];
  };
}

export function DataTableHeader({ table }: ServicesTableHeaderProps) {
  return (
    <TableHeader className="bg-secondary">
      <TableRow>
        {table.columns.map((column) => (
          <TableHead key={column.id}>
            <TableHeaderCell column={column} />
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  );
}
