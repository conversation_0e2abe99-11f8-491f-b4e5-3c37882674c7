import { Badge } from "@/components/ui/badge";

export const statusColumn: NativeTableColumn<Service> = {
  id: "status",
  accessorKey: "status",
  header: "Status",
  enableSorting: true,
  enableFiltering: true,
  filterFn: (row, _columnId, filterValue) => {
    if (!filterValue || filterValue === "" || filterValue === "all")
      return true;

    const service = row.original;

    if (filterValue === "published") {
      return service.isPublished === true;
    }
    if (filterValue === "draft") {
      return service.isPublished === false;
    }

    const serviceStatus = service.status?.toLowerCase();
    return serviceStatus === String(filterValue).toLowerCase();
  },
  cell: ({ row }) => {
    const service = row.original;
    const status = service.isPublished ? "published" : "draft";
    const variant = status === "published" ? "default" : "secondary";

    return (
      <Badge variant={variant} className="text-xs">
        {status === "published" ? "Published" : "Draft"}
      </Badge>
    );
  },
  size: 120,
};
