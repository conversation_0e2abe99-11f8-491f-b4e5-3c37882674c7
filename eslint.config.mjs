import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Ignore UI component folder completely
  {
    ignores: ["**/components/ui/**", "**/ui/**", "**/*.ui.*"],
  },

  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    plugins: {
      react: (await import("eslint-plugin-react")).default,
    },
    rules: {
      // Enforce maximum lines per function/component
      "max-lines-per-function": [
        "warn",
        {
          max: 200,
          skipBlankLines: true,
          skipComments: true,
          IIFEs: true,
        },
      ],

      // Limit file length
      "max-lines": [
        "warn",
        {
          max: 300,
          skipBlankLines: true,
          skipComments: true,
        },
      ],

      // Limit complexity
      complexity: ["warn", { max: 10 }],

      // Limit nested depth
      "max-depth": ["warn", { max: 4 }],

      // Limit number of function parameters
      "max-params": ["warn", { max: 5 }],

      // React: Format props (one per line when multiline)
      "react/jsx-max-props-per-line": [
        "warn",
        {
          maximum: 1,
          when: "multiline",
        },
      ],

      // React: Limit props indentation and formatting
      "react/jsx-first-prop-new-line": ["warn", "multiline-multiprop"],
      "react/jsx-closing-bracket-location": ["warn", "tag-aligned"],

      // Custom rule to warn about too many props (manual enforcement)
      "prefer-const": "warn", // This is a placeholder - see alternative approaches below

      // Allow unused variables/parameters when prefixed with underscore
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
        },
      ],

      // Disallow console statements (error level)
      // You can customize this rule to allow specific console methods if needed:
      // "no-console": ["error", { "allow": ["warn", "error"] }]
      "no-console": "off",
      "no-console": "off",

      // Disable problematic react-hooks rule that has compatibility issues with ESLint 9.x
      "react-hooks/exhaustive-deps": "off",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
  // Allow console statements in test files and Cypress files
  {
    files: [
      "**/*.test.{js,jsx,ts,tsx}",
      "**/*.spec.{js,jsx,ts,tsx}",
      "**/cypress/**/*.{js,jsx,ts,tsx}",
      "**/__tests__/**/*.{js,jsx,ts,tsx}",
      "**/*-example.{js,jsx,ts,tsx}",
    ],
    rules: {
      "no-console": "off",
    },
  },
];

export default eslintConfig;
