# NextJS Clean Architecture Documentation

## Table of Contents

1. [Overview](#overview)
2. [Tech Stack](#tech-stack)
3. [Project Structure](#project-structure)
4. [Clean Architecture Principles](#clean-architecture-principles)
5. [Data Fetching Strategy](#data-fetching-strategy)
6. [State Management](#state-management)
7. [Service Layer Architecture](#service-layer-architecture)
8. [UI Components Structure](#ui-components-structure)
9. [Internationalization](#internationalization)
10. [Testing Strategy](#testing-strategy)
11. [Development Guidelines](#development-guidelines)
12. [Performance Optimization](#performance-optimization)

## Overview

This project implements a modern NextJS application following clean architecture principles, emphasizing separation of concerns, testability, and maintainability. The architecture leverages NextJS 15+ features including App Router, Server Components, and Server Actions.

## Tech Stack

### Core Technologies

- **NextJS 15+** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **React 19+** - UI library with Concurrent Features

### Styling & UI

- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Re-usable component library
- **Lucide React** - Icon library

### State Management

- **Zustand** - Lightweight state management
- **React Hook Form** - Form state management
- **TanStack Query** - Server state management (optional)

### Internationalization

- **next-intl** - Internationalization library
- **Namespace-based translations** - Organized translation files

### Testing

- **Jest** - Unit testing framework
- **React Testing Library** - Component testing
- **Playwright** - End-to-end testing
- **MSW (Mock Service Worker)** - API mocking

### Development Tools

- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Husky** - Git hooks
- **lint-staged** - Staged files linting

## Project Structure

```code
src/
├── app/                          # NextJS App Router
│   ├── [locale]/                 # Internationalized routes
│   │   ├── (auth)/              # Route groups
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── dashboard/
│   │   │   ├── page.tsx
│   │   │   └── loading.tsx
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── api/                     # API routes (if needed)
│   └── not-found.tsx
├── components/                   # Reusable UI components
│   ├── ui/                      # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   └── ...
│   ├── forms/                   # Form components
│   ├── layout/                  # Layout components
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   └── footer.tsx
│   └── features/                # Feature-specific components
│       ├── auth/
│       ├── dashboard/
│       └── profile/
├── hooks/                       # Custom React hooks
│   ├── use-auth.ts
│   ├── use-local-storage.ts
│   └── use-debounce.ts
├── lib/                         # Utility functions
│   ├── utils.ts                 # General utilities
│   ├── validations.ts           # Zod schemas
│   ├── constants.ts             # App constants
│   └── config.ts                # Configuration
├── services/                    # API service layer
│   ├── base/
│   │   ├── base-service.ts      # Base service class
│   │   ├── http-client.ts       # HTTP client wrapper
│   │   └── types.ts             # Service types
│   ├── auth/
│   │   ├── auth-service.ts
│   │   └── types.ts
│   ├── user/
│   │   ├── user-service.ts
│   │   └── types.ts
│   └── index.ts                 # Service exports
├── store/                       # Zustand stores
│   ├── auth-store.ts
│   ├── ui-store.ts
│   └── index.ts
├── server/                      # Server-side code
│   ├── actions/                 # Server actions
│   │   ├── auth-actions.ts
│   │   └── user-actions.ts
│   ├── utils/
│   └── db/                      # Database utilities
├── types/                       # TypeScript type definitions
│   ├── auth.ts
│   ├── user.ts
│   └── api.ts
├── messages/                    # Internationalization
│   ├── en.json
│   ├── fr.json
│   └── es.json
└── __tests__/                   # Test files
    ├── __mocks__/
    ├── components/
    ├── services/
    ├── utils/
    └── e2e/
```

## Clean Architecture Principles

### 1. Separation of Concerns

- **Presentation Layer**: React components, pages, and UI logic
- **Application Layer**: Custom hooks, state management, and business logic
- **Infrastructure Layer**: API services, external integrations
- **Domain Layer**: Business entities, types, and validation rules

### 2. Dependency Inversion

- High-level modules don't depend on low-level modules
- Both depend on abstractions (interfaces/types)
- Services implement interfaces, components consume abstractions

### 3. Single Responsibility

- Each component, hook, and service has one clear purpose
- Utilities are focused and reusable
- Clear boundaries between concerns

## Data Fetching Strategy

### Server-Side Rendering (SSR)

```typescript
// app/dashboard/page.tsx
import { UserService } from '@/services/user/user-service'

export default async function DashboardPage() {
  const users = await UserService.getUsers()

  return <UserList users={users} />
}
```

### Server Actions

```typescript
// server/actions/user-actions.ts
"use server";

import { UserService } from "@/services/user/user-service";
import { revalidatePath } from "next/cache";

export async function createUser(formData: FormData) {
  const user = await UserService.createUser({
    name: formData.get("name") as string,
    email: formData.get("email") as string,
  });

  revalidatePath("/users");
  return user;
}
```

### Client-Side Fetching

```typescript
// hooks/use-users.ts
import { useEffect, useState } from "react";
import { UserService } from "@/services/user/user-service";

export function useUsers() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    UserService.getUsers()
      .then(setUsers)
      .finally(() => setLoading(false));
  }, []);

  return { users, loading };
}
```

## State Management

### Zustand Store Structure

```typescript
// store/auth-store.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (user: User, token: string) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      login: (user, token) => set({ user, token, isAuthenticated: true }),
      logout: () => set({ user: null, token: null, isAuthenticated: false }),
    }),
    { name: "auth-storage" },
  ),
);
```

### Store Organization

- **auth-store.ts**: Authentication state
- **ui-store.ts**: UI state (modals, sidebar, theme)
- **user-store.ts**: User-related state
- **feature-stores/**: Feature-specific stores

## Service Layer Architecture

### Base Service Class

```typescript
// services/base/base-service.ts
export abstract class BaseService {
  protected baseURL: string;
  protected headers: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.headers = {
      "Content-Type": "application/json",
    };
  }

  protected async request<T>(
    endpoint: string,
    options?: RequestInit,
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const response = await fetch(url, {
      headers: this.headers,
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  protected get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "GET" });
  }

  protected post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  protected put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  protected delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }
}
```

### Specific Service Implementation

```typescript
// services/user/user-service.ts
import { BaseService } from "../base/base-service";
import type { User, CreateUserData, UpdateUserData } from "./types";

class UserServiceClass extends BaseService {
  constructor() {
    super(process.env.NEXT_PUBLIC_API_URL!);
  }

  async getUsers(): Promise<User[]> {
    return this.get<User[]>("/users");
  }

  async getUserById(id: string): Promise<User> {
    return this.get<User>(`/users/${id}`);
  }

  async createUser(data: CreateUserData): Promise<User> {
    return this.post<User>("/users", data);
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    return this.put<User>(`/users/${id}`, data);
  }

  async deleteUser(id: string): Promise<void> {
    return this.delete<void>(`/users/${id}`);
  }
}

export const UserService = new UserServiceClass();
```

## UI Components Structure

### Component Hierarchy

```
components/
├── ui/                    # Basic UI components (shadcn/ui)
├── forms/                 # Form components
│   ├── LoginForm.tsx
│   └── UserForm.tsx
├── layout/                # Layout components
│   ├── Header.tsx
│   ├── Sidebar.tsx
│   └── Footer.tsx
└── features/              # Feature components
    ├── auth/
    │   ├── LoginPage.tsx
    └── users/
        ├── UserList.tsx
        └── UserCard.tsx
```

### Component Guidelines

- Use TypeScript interfaces for props
- Implement proper error boundaries
- Follow composition over inheritance
- Use forwardRef for ref forwarding
- Implement proper accessibility

## Internationalization

### Configuration

```typescript
// lib/i18n.ts
import { notFound } from "next/navigation";
import { getRequestConfig } from "next-intl/server";

const locales = ["en", "fr", "es"];

export default getRequestConfig(async ({ locale }) => {
  if (!locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
```

### Namespace Organization

```json
// messages/en.json
{
  "common": {
    "submit": "Submit",
    "cancel": "Cancel",
    "loading": "Loading..."
  },
  "auth": {
    "login": "Login",
    "logout": "Logout",
    "register": "Register",
    "forgotPassword": "Forgot Password?"
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome back, {name}!"
  },
  "users": {
    "title": "Users",
    "addUser": "Add User",
    "editUser": "Edit User",
    "deleteUser": "Delete User"
  }
}
```

### Usage in Components

```typescript
import { useTranslations } from 'next-intl'

export function LoginForm() {
  const t = useTranslations('auth')

  return (
    <form>
      <button type="submit">
        {t('login')}
      </button>
    </form>
  )
}
```

## Testing Strategy

### Unit Testing Setup

```javascript
// jest.config.js
const nextJest = require("next/jest");

const createJestConfig = nextJest({
  dir: "./",
});

const customJestConfig = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
  testEnvironment: "jest-environment-jsdom",
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/src/$1",
  },
};

module.exports = createJestConfig(customJestConfig);
```

### Component Testing

```typescript
// __tests__/components/LoginForm.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { LoginForm } from '@/components/forms/LoginForm'

describe('LoginForm', () => {
  it('renders login form', () => {
    render(<LoginForm />)
    expect(screen.getByRole('textbox', { name: /email/i })).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
  })

  it('submits form with valid data', async () => {
    const mockSubmit = jest.fn()
    render(<LoginForm onSubmit={mockSubmit} />)

    fireEvent.change(screen.getByRole('textbox', { name: /email/i }), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    })
    fireEvent.click(screen.getByRole('button', { name: /login/i }))

    expect(mockSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })
})
```

### Service Testing

```typescript
// __tests__/services/user-service.test.ts
import { UserService } from "@/services/user/user-service";

// Mock fetch
global.fetch = jest.fn();

describe("UserService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("fetches users successfully", async () => {
    const mockUsers = [{ id: "1", name: "John Doe" }];
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockUsers,
    });

    const users = await UserService.getUsers();
    expect(users).toEqual(mockUsers);
    expect(fetch).toHaveBeenCalledWith("/api/users", expect.any(Object));
  });
});
```

### E2E Testing with Playwright

```typescript
// __tests__/e2e/auth.spec.ts
import { test, expect } from "@playwright/test";

test.describe("Authentication", () => {
  test("user can login successfully", async ({ page }) => {
    await page.goto("/login");

    await page.fill('[data-testid="email-input"]', "<EMAIL>");
    await page.fill('[data-testid="password-input"]', "password123");
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL("/dashboard");
    await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
  });
});
```

## Development Guidelines

### Code Style

- Use ESLint and Prettier for consistent formatting
- Follow TypeScript strict mode
- Use meaningful variable and function names
- Write self-documenting code with clear interfaces

### Git Workflow

- Use conventional commits (feat, fix, docs, etc.)
- Create feature branches from main
- Use pull requests for code review
- Run tests before merging

### Performance Guidelines

- Use React.memo for expensive components
- Implement proper loading states
- Optimize images with next/image
- Use dynamic imports for code splitting
- Implement proper caching strategies

### Security Practices

- Validate all inputs (client and server)
- Use HTTPS in production
- Implement proper authentication
- Sanitize user-generated content
- Use environment variables for secrets

## Performance Optimization

### NextJS Optimizations

- Use Server Components by default
- Implement proper caching with `revalidate`
- Use `loading.tsx` for better UX
- Optimize bundle size with dynamic imports

### React Optimizations

- Use React.memo for pure components
- Implement useCallback and useMemo strategically
- Avoid unnecessary re-renders
- Use Suspense for better loading states

### Build Optimizations

- Configure proper bundling
- Use tree shaking
- Optimize images and fonts
- Implement proper caching headers
