"use client";

import {
  FormLabel,
  <PERSON>der,
  FormControl,
  FormDescription,
  FormMessage,
  FormItem,
  Textarea,
  TextareaProps,
  FormField,
} from "@/components";
import { Control, FieldValues, Path } from "react-hook-form";
import { ReactNode } from "react";
import { cn } from "@/lib";

interface FieldIconProps {
  icon: ReactNode;
  position: "start" | "end";
}

interface TextareaField<T extends FieldValues> extends TextareaProps {
  control: Control<T>;
  field_name: Path<T>;
  label?: string;
  description?: string;
  icon?: FieldIconProps;
  apply_password_strength?: boolean;
}

export function TextareaField<T extends FieldValues>(props: TextareaField<T>) {
  const { control, field_name, label, description, icon, className, ...rest } =
    props;

  return (
    <FormField
      control={control}
      name={field_name}
      render={({ field }) => (
        <FormItem>
          {label ? <FormLabel>{label}</FormLabel> : null}
          <FormControl className="relative mt-1.5">
            <div className="relative">
              <div className="relative">
                <Textarea
                  {...field}
                  {...rest}
                  className={cn(
                    icon?.position === "start" ? "pl-10" : "pr-10",
                    className,
                  )}
                />
                <Render>
                  <Render.If condition={icon ? true : false}>
                    <Render>
                      <Render.If condition={icon?.position === "start"}>
                        <div className="absolute top-4 left-4 transform text-secondary bg-gray-8">
                          {" "}
                          {icon?.icon}
                        </div>
                      </Render.If>
                      <Render.Else>
                        <div className="absolute top-4 right-4 transform text-secondary bg-gray-8">
                          {icon?.icon}
                        </div>
                      </Render.Else>
                    </Render>
                  </Render.If>
                </Render>
              </div>
            </div>
          </FormControl>
          {description ? (
            <FormDescription>{description}</FormDescription>
          ) : null}
          <FormMessage useTranslationKey={true} />
        </FormItem>
      )}
    />
  );
}
