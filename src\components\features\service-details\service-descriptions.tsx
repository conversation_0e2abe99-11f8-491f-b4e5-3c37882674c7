import { useTranslations } from "next-intl";

interface ServiceDescriptionsProps {
  service: Service;
}

export function ServiceDescriptions({ service }: ServiceDescriptionsProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <>
      {/* Service Title */}
      <div className="flex flex-col w-full">
        <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
          {t("serviceName")} :
        </label>
        <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full">
          <span className="text-xs text-foreground font-['Poppins']">
            {service.name}
          </span>
        </div>
      </div>

      {/* Service Description */}
      <div className="flex flex-col w-full">
        <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
          {t("longDescription")} :
        </label>
        <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full min-h-[120px]">
          <p className="text-xs text-foreground font-['Poppins'] leading-relaxed">
            {service.longDescription || service.description}
          </p>
        </div>
      </div>
    </>
  );
}
