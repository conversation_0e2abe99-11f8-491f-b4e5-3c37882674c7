"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";

interface TableHeaderCellProps<TData = unknown> {
  column: NativeTableColumnInstance<TData>;
}

export function TableHeaderCell<TData = unknown>({
  column,
}: TableHeaderCellProps<TData>) {
  const sortDirection = column.getIsSorted();
  const canSort = column.enableSorting !== false;

  const handleSort = () => {
    if (canSort) {
      column.toggleSorting();
    }
  };

  const renderHeader = () => {
    if (typeof column.header === "function") {
      return column.header({ column });
    }
    return column.header;
  };

  if (!canSort) {
    return <div className="font-medium text-foreground">{renderHeader()}</div>;
  }

  return (
    <Button
      variant="ghost"
      onClick={handleSort}
      className="h-auto p-0 font-medium text-foreground hover:bg-transparent flex items-center gap-1"
    >
      {renderHeader()}
      {sortDirection === "asc" && <ChevronUp className="h-4 w-4" />}
      {sortDirection === "desc" && <ChevronDown className="h-4 w-4" />}
      {sortDirection === false && canSort && (
        <div className="h-4 w-4" /> // Espace réservé pour l'icône
      )}
    </Button>
  );
}
