import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { ChevronDown } from "lucide-react";
import Link from "next/link";

interface CollapsibleNavigationItemProps {
  item: NavigationItem;
  isSectionActive: (name: string, href?: string) => boolean;
  isLinkActive: (href: string) => boolean;
  t: (key: string) => string;
}

export const CollapsibleNavigationItem = ({
  item,
  isSectionActive,
  isLinkActive,
  t,
}: CollapsibleNavigationItemProps) => {
  return (
    <Collapsible
      defaultOpen={item.defaultOpen || false}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            className={`w-full justify-between ${
              isSectionActive(item.titleKey)
                ? "bg-sidebar-primary text-sidebar-primary-foreground"
                : ""
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center size-6 rounded-lg">
                <item.icon className="size-4" />
              </div>
              <span>{t(item.titleKey)}</span>
            </div>
            <ChevronDown className="size-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        {item.items?.length && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items.map((subItem: NavigationSubItem) => (
                <SidebarMenuSubItem key={subItem.titleKey}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={isLinkActive(subItem.href)}
                  >
                    {subItem.isDetails ? (
                      <span className="hover:text-sidebar-foreground">
                        {t(subItem.titleKey)}
                      </span>
                    ) : (
                      <Link href={subItem.href}>{t(subItem.titleKey)}</Link>
                    )}
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
};
