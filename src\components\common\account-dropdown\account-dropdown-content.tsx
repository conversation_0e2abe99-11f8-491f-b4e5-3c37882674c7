import { LogOut, User } from "lucide-react";
import { useTranslations } from "next-intl";
import { MenuItem } from "./account-dropdown-menu-item";

type DropdownContentProps = {
  isSignedIn: boolean;
  onSignIn: () => void;
  onSignOut: () => void;
};

export const DropdownContent = ({
  isSignedIn,
  onSignIn,
  onSignOut,
}: DropdownContentProps) => {
  const t = useTranslations("Header");

  return (
    <div className="flex flex-col gap-1 p-2">
      {isSignedIn ? (
        <>
          <MenuItem
            icon={<User className="mr-2 h-4 w-4" />}
            label={t("myAccount")}
          />
          <MenuItem
            onClick={onSignOut}
            icon={<LogOut className="mr-2 h-4 w-4" />}
            label={t("signOut")}
          />
        </>
      ) : (
        <MenuItem
          onClick={onSignIn}
          icon={<LogOut className="mr-2 h-4 w-4" />}
          label={t("signIn")}
        />
      )}
    </div>
  );
};
