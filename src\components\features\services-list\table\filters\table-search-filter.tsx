import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface TableSearchFilterProps {
  table: {
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
    searchColumn: string;
    setSearchColumn: (value: string) => void;
  };
}

export function TableSearchFilter({ table }: TableSearchFilterProps) {
  const t = useTranslations("Services");

  // Options de colonnes de recherche
  const searchOptions = [
    { value: "description", label: t("service") || "Service" },
    { value: "provider", label: t("provider") || "Provider" },
    { value: "categories", label: t("category") || "Category" },
  ];

  // Placeholder dynamique selon la colonne sélectionnée
  const getPlaceholder = () => {
    switch (table.searchColumn) {
      case "description": {
        return t("filterByServiceName") || "Search by service...";
      }
      case "provider": {
        return t("filterByProvider") || "Search by provider...";
      }
      case "categories": {
        return t("filterByCategory") || "Search by category...";
      }
      default: {
        return t("search") || "Search...";
      }
    }
  };

  return (
    <div
      className={cn(
        "flex items-center gap-2 relative bg-transparent transition-colors text-base rounded-md border border-input pl-3 h-9 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed md:text-sm has-[input:focus]:outline-none has-[input:focus]:ring-1 has-[input:focus]:ring-ring",
        "w-sm max-w-sm",
      )}
    >
      {/* Sélecteur de colonne intégré */}
      <Select value={table.searchColumn} onValueChange={table.setSearchColumn}>
        <SelectTrigger className="w-auto h-auto border-none bg-transparent p-0 shadow-none focus:ring-0 ring-transparent focus:ring-offset-0 [&>svg]:h-3 [&>svg]:w-3">
          <SelectValue className="text-xs text-muted-foreground" />
        </SelectTrigger>
        <SelectContent>
          {searchOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Séparateur visuel */}
      <div className="h-4 w-px bg-border" />

      {/* Champ de recherche intégré */}
      <input
        type="text"
        placeholder={getPlaceholder()}
        value={table.globalFilter}
        onChange={(event) => table.setGlobalFilter(event.target.value)}
        className="flex w-full border-none bg-transparent text-base transition-colors placeholder:text-muted-foreground outline-none h-9 py-1 p-0 leading-none md:text-sm"
      />
    </div>
  );
}
