import { Button } from "@/components/ui/button";
import { redirect } from "next/navigation";

export const actionsColumn: NativeTableColumn<Service> = {
  id: "actions",
  header: "Actions",
  enableSorting: false,
  enableFiltering: false,
  cell: ({ row }) => (
    <Button
      variant="outline"
      size="sm"
      onClick={() => redirect(`/services/${row.original.id}`)}
      className="h-8 px-3 text-xs"
    >
      View More
    </Button>
  ),
  size: 100,
};
