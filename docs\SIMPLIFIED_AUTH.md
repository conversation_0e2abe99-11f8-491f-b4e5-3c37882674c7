# Simplified Authentication System

## Overview

This document describes the simplified authentication system that focuses only on basic authentication without RBAC (Role-Based Access Control) or token persistence.

## Current Implementation

### ✅ What's Implemented

1. **Basic Authentication Flow**
   - User login with email/password
   - User logout
   - Authentication state management
   - Error handling

2. **React Context Integration**
   - `AuthProvider` component for state management
   - `useAuth` hook for accessing authentication state
   - `useAuthStatus` hook for simplified status checking
   - `useAuthActions` hook for login/logout with navigation

3. **Protected Routes**
   - `ProtectedRoute` component for route protection
   - `SignedIn` and `SignedOut` components for conditional rendering
   - Basic loading states and error handling

4. **Server Actions Integration**
   - Integration with existing server actions
   - Proper error handling and response processing

### 🚫 What's NOT Implemented (TODO)

1. **Token Persistence**
   - No localStorage/sessionStorage
   - No automatic token refresh
   - Session-only authentication (lost on page refresh)

2. **Role-Based Access Control (RBAC)**
   - No role checking
   - No permission verification
   - All authenticated users have full access

3. **Advanced Features**
   - No token expiration handling
   - No automatic logout on token expiry
   - No remember me functionality

## File Structure

```
src/
├── providers/
│   └── auth-provider.tsx          # Main authentication provider
├── hooks/
│   └── auth-context.ts           # Authentication hooks
├── components/auth/
│   ├── protected-route.tsx       # Route protection components
│   └── auth-error-boundary.tsx   # Error handling (existing)
├── lib/
│   └── auth-utils.ts            # Utility functions (simplified)
└── actions/
    └── auth-actions.ts          # Server actions (existing)
```

## Usage Examples

### 1. Basic Authentication

```tsx
import { useAuth } from '@/hooks/auth-context';

function LoginForm() {
  const { login, isLoading, error } = useAuth();

  const handleSubmit = async (credentials) => {
    const result = await login(credentials);
    if (result.success) {
      // User is now authenticated
      router.push('/dashboard');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
      {error && <div className="error">{error}</div>}
      <button disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

### 2. Protected Routes

```tsx
import { ProtectedRoute } from '@/components/auth';

function DashboardPage() {
  return (
    <ProtectedRoute>
      <div>This content is only visible to authenticated users</div>
    </ProtectedRoute>
  );
}
```

### 3. Conditional Rendering

```tsx
import { SignedIn, SignedOut } from '@/components/auth';

function Navigation() {
  return (
    <nav>
      <SignedIn>
        <button onClick={logout}>Logout</button>
      </SignedIn>
      <SignedOut>
        <Link href="/login">Login</Link>
      </SignedOut>
    </nav>
  );
}
```

### 4. Authentication Status

```tsx
import { useAuthStatus } from '@/hooks/auth-context';

function UserProfile() {
  const { isSignedIn, isLoading } = useAuthStatus();

  if (isLoading) return <div>Loading...</div>;
  if (!isSignedIn) return <div>Please log in</div>;

  return <div>Welcome, authenticated user!</div>;
}
```

## Testing

Use the provided test script to verify the authentication system:

```bash
node scripts/test-auth.js
```

This script will:
- Test API endpoint availability
- Test login functionality
- Test token validation
- Provide troubleshooting information

## Future Enhancements (TODO)

### Phase 2: Token Persistence
- Add localStorage/sessionStorage support
- Implement automatic token refresh
- Add token expiration handling
- Persist authentication state across page refreshes

### Phase 3: Role-Based Access Control (RBAC)
- Add role checking utilities
- Implement permission verification
- Create role-based route protection
- Add admin/user role differentiation

### Phase 4: Advanced Features
- Remember me functionality
- Multi-factor authentication
- Session management
- Advanced error handling

## Migration Notes

When upgrading to future phases:

1. **Adding Persistence**: Update `AuthProvider` to use `TokenStorage`
2. **Adding RBAC**: Uncomment and implement role utilities in `auth-utils.ts`
3. **Advanced Features**: Extend the existing hooks and components

## Security Considerations

Current security limitations:
- No token persistence means users must re-login on page refresh
- No automatic logout on token expiry
- No protection against XSS attacks (no httpOnly cookies)

These will be addressed in future phases.

## Support

For issues or questions about the authentication system:
1. Check the test script output for troubleshooting
2. Verify API server is running and accessible
3. Ensure credentials are correct
4. Check browser console for errors
