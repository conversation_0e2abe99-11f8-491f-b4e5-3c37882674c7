export function formatTableDate(dateString?: string | null): string {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${year} - ${hours}:${minutes}`;
}

interface TableDateCellProps {
  dateString?: string | null;
}

export function TableDateCell({ dateString }: TableDateCellProps) {
  return <span>{formatTableDate(dateString)}</span>;
}
