import { BaseApiService as BaseService } from "../base-service";
import { DEFAULT_AUTH_CONFIG } from "@/config/env";
import { CookieStorage } from "./token-storage";

// All types are now available globally from src/types/auth/index.d.ts
// No need for local type definitions

/**
 * Real API Authentication Service
 * Replaces the mock authentication with actual API calls
 * Maintains the same interface for backward compatibility
 */
export class AuthService extends BaseService {
  private static instance: AuthService;
  private readonly apiUrl: string;
  private readonly endpoints = DEFAULT_AUTH_CONFIG.endpoints;

  constructor() {
    super();
    this.apiUrl = DEFAULT_AUTH_CONFIG.apiUrl;
  }

  // Singleton pattern to ensure single instance
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Authenticate user with email and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const loginData: AdminLoginRequest = {
        email: credentials.email,
        password: credentials.password,
      };

      const response = await fetch(`${this.apiUrl}${this.endpoints.login}`, {
        method: "POST",
        headers: DEFAULT_AUTH_CONFIG.headers,
        body: JSON.stringify(loginData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: {
            code:
              response.status === 401 ? "INVALID_CREDENTIALS" : "LOGIN_ERROR",
            message: errorData.message || "Login failed",
          },
        };
      }

      const authResponse: AuthResponse = await response.json();

      // Validate response structure
      if (!this.isValidAuthResponse(authResponse)) {
        return {
          success: false,
          error: {
            code: "INVALID_RESPONSE",
            message: "Invalid response format from server",
          },
        };
      }

      // Store tokens using TokenStorage
      CookieStorage.setTokens(authResponse);

      return {
        success: true,
        data: {
          admin: authResponse.admin,
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
          expiresAt: authResponse.expiresAt,
          message: "Login successful",
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: "LOGIN_ERROR",
          message: "An error occurred during login. Please try again.",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<LogoutResponse> {
    try {
      // Clear stored tokens
      CookieStorage.clear();

      return {
        success: true,
        message: "Logout successful",
      };
    } catch (_error) {
      return {
        success: false,
        message: "An error occurred during logout",
      };
    }
  }

  /**
   * Get current authenticated admin
   */
  async getCurrentUser(): Promise<Admin | null> {
    try {
      const authData = await CookieStorage.getAuthData();

      if (!authData.isAuthenticated || !authData.user) {
        return null;
      }

      return authData.user;
    } catch (error) {
      console.error("Get current user error:", error);
      return null;
    }
  }

  /**
   * Validate authentication token
   */
  async validateToken(token: string): Promise<ValidateTokenResponse> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.profile}`, {
        method: "GET",
        headers: {
          ...DEFAULT_AUTH_CONFIG.headers,
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return {
          valid: false,
          error: {
            code: "INVALID_TOKEN",
            message: "Token is invalid or expired",
          },
        };
      }

      const admin: Admin = await response.json();

      return {
        valid: true,
        admin,
      };
    } catch (error) {
      return {
        valid: false,
        error: {
          code: "TOKEN_VALIDATION_ERROR",
          message: "Error validating token",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  /**
   * Get current access token
   */
  async getCurrentToken(): Promise<string | null> {
    const authData = await CookieStorage.getAuthData();
    return authData.isAuthenticated ? authData.accessToken : null;
  }

  /**
   * Validate AuthResponse structure
   */
  private isValidAuthResponse(data: unknown): data is AuthResponse {
    if (!data || typeof data !== "object") return false;

    const obj = data as Record<string, unknown>;

    return (
      typeof obj.accessToken === "string" &&
      typeof obj.refreshToken === "string" &&
      !!obj.admin &&
      typeof obj.admin === "object" &&
      obj.admin !== null &&
      typeof (obj.admin as Record<string, unknown>).id === "number" &&
      typeof (obj.admin as Record<string, unknown>).email === "string" &&
      typeof obj.expiresAt === "string"
    );
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
