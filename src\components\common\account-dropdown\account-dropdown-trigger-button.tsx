import { Button } from "@/components/ui";
import { Menu } from "lucide-react";

type DropdownTriggerButtonProps = {
  children: React.ReactNode;
};

export function DropdownTriggerButton({
  children,
}: DropdownTriggerButtonProps) {
  return (
    <Button
      variant="outline"
      className="h-auto px-3 py-1.5gap-2 border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
    >
      <Menu className="h-4 w-4" />
      {children}
    </Button>
  );
}
