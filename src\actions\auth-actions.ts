"use server";

import { redirect } from "next/navigation";
import {
  authenticateUser,
  logoutUser,
  isUserAuthenticated,
  getCurrentUserSession,
  validateAuthToken,
  refreshUserToken,
} from "./auth";
import { FormLoginData } from "@/schemas";

/**
 * Navigation and Utility Actions
 * Handles authentication with navigation and utility functions
 * Uses the organized auth actions following clean code principles
 */

/**
 * Server action for user login
 * Wrapper around authenticate<PERSON>ser for backward compatibility
 */
export async function login(
  credentials: FormLoginData,
): Promise<LoginResponse> {
  return await authenticateUser(credentials);
}

/**
 * Server action for user logout
 * Wrapper around logoutUser for backward compatibility
 */
export async function logout(): Promise<LogoutResponse> {
  return await logoutUser();
}

/**
 * Server action to check if user is authenticated
 * Wrapper around isUserAuthenticated for backward compatibility
 */
export async function getIsAuthenticated(): Promise<boolean> {
  return await isUserAuthenticated();
}

/**
 * Helper function to construct login URL with redirect parameter
 */
function constructLoginUrl(redirectTo: string): string {
  // Parse the login path to handle existing query parameters
  const [loginPath, existingQuery] = "/login".split("?");
  const params = new URLSearchParams(existingQuery || "");
  params.set("redirect", redirectTo);
  return `${loginPath}?${params.toString()}`;
}

/**
 * Server action to redirect to login if not authenticated
 */
export async function requireAuth(redirectTo?: string) {
  const isAuthenticated = await getIsAuthenticated();

  if (!isAuthenticated) {
    const loginUrl = redirectTo ? constructLoginUrl(redirectTo) : "/login";
    redirect(loginUrl);
  }
}

/**
 * Server action for logout with redirect
 */
export async function startLogout() {
  await logout();
  redirect("/login");
}

/**
 * Server action to validate authentication token
 * Wrapper around validateAuthToken for backward compatibility
 */
export async function validateToken(
  token: string,
): Promise<ValidateTokenResponse> {
  return await validateAuthToken(token);
}

/**
 * Server action for token refresh
 * Wrapper around refreshUserToken for backward compatibility
 */
export async function refreshAuthToken(): Promise<LoginResponse> {
  return await refreshUserToken();
}

/**
 * Server action to get current user profile
 * Wrapper around getCurrentUserSession for backward compatibility
 */
export async function getCurrentUser(): Promise<ValidateTokenResponse> {
  return await getCurrentUserSession();
}
