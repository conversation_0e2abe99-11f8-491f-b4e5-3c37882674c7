"use client";

import { Button } from "@/components/ui";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

interface ServicesDetailsErrorProps {
  error: Error;
  serviceId?: string;
}

export function ServicesDetailsError({
  error,
  serviceId,
}: ServicesDetailsErrorProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();

  const handleBack = () => {
    router.push("/services");
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <div className="text-lg text-destructive">{t("error")}</div>
      {serviceId && (
        <div className="text-sm text-muted-foreground">
          Service ID: {serviceId}
        </div>
      )}
      <div className="text-sm text-muted-foreground">
        Error: {error?.message || "Service not found"}
      </div>
      <Button onClick={handleBack} variant="outline">
        {t("backToServices")}
      </Button>
    </div>
  );
}
