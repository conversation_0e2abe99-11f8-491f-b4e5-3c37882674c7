"use client";

import { useLocale, useTranslations } from "next-intl";
import { LocaleSwitcherSelect } from "../ui";

export function LocaleSwitcher() {
  const t = useTranslations("LocaleSwitcher");
  const locale = useLocale();

  return (
    <LocaleSwitcherSelect
      defaultValue={locale}
      items={[
        {
          value: "fr",
          label: t("fr"),
          flagUrl: "/flags/fr.png",
        },
        {
          value: "de",
          label: t("de"),
          flagUrl: "/flags/de.png",
        },
        {
          value: "en",
          label: t("en"),
          flagUrl: "/flags/en.png",
        },
        {
          value: "code",
          label: t("code"),
          flagUrl: "/logo.jpg",
        },
      ]}
      label={t("label")}
    />
  );
}
