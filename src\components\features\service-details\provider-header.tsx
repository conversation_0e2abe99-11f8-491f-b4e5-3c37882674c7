import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";

interface ProviderHeaderProps {
  provider: Provider;
}

export function ProviderHeader({ provider }: ProviderHeaderProps) {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <div className="flex flex-col justify-center items-center gap-2.5 flex-1 h-full rounded-[10px]">
      <Avatar className="w-[150px] h-[150px] border border-border">
        <AvatarImage
          src={provider.imageUrl || undefined}
          alt={`${provider.firstName} ${provider.lastName}`}
        />
        <AvatarFallback className="text-4xl font-['Poppins'] text-foreground">
          {getInitials(provider.firstName, provider.lastName)}
        </AvatarFallback>
      </Avatar>
    </div>
  );
}
