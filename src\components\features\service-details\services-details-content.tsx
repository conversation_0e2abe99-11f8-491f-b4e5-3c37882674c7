import { ServiceHeader } from "./service-header";
import { ServiceInformation } from "./service-information";
import { ProviderInformation } from "./provider-information";

interface ServicesDetailsContentProps {
  service: Service;
  onBack: () => void;
}

export function ServicesDetailsContent({
  service,
  onBack,
}: ServicesDetailsContentProps) {
  return (
    <div className="flex flex-col items-center gap-2.5 px-5 pb-2.5">
      <ServiceHeader onBack={onBack} />
      <ProviderInformation provider={service.provider} />
      <ServiceInformation service={service} />
    </div>
  );
}
