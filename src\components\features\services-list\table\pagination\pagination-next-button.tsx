import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

interface PaginationNextButtonProps {
  onClick: () => void;
  disabled: boolean;
}

export function PaginationNextButton({
  onClick,
  disabled,
}: PaginationNextButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      disabled={disabled}
      className="w-6 h-6 p-0 bg-white border border-[#EDEDED] rounded-[5px] disabled:opacity-50"
    >
      <ChevronRight className="w-3 h-3 text-[#373737]" />
    </Button>
  );
}
