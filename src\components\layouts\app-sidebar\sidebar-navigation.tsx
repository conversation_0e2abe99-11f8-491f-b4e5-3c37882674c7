import { SimpleNavigationItem } from "./sidebar-navigation-item";
import { CollapsibleNavigationItem } from "./sidebar-collapsible-item";

interface NavigationItemProps {
  item: NavigationItem;
  isSectionActive: (name: string, href?: string) => boolean;
  isLinkActive: (href: string) => boolean;
  t: (key: string) => string;
}

export const NavigationItem = ({
  item,
  isSectionActive,
  isLinkActive,
  t,
}: NavigationItemProps) => {
  if (item.items === undefined) {
    return (
      <SimpleNavigationItem
        item={item}
        isSectionActive={isSectionActive}
        t={t}
      />
    );
  }

  return (
    <CollapsibleNavigationItem
      item={item}
      isSectionActive={isSectionActive}
      isLinkActive={isLinkActive}
      t={t}
    />
  );
};
