import React, { ReactNode } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabelProps,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  RadioGroupItemProps,
  RadioGroupProps,
} from "@/components";
import { Control, FieldValues, Path } from "react-hook-form";
import { cn } from "@/lib";

interface RadioOption {
  label: ReactNode;
  value: string;
}
interface RadioGroupField<T extends FieldValues> extends RadioGroupProps {
  control: Control<T>;
  fieldName: Path<T>;
  options: RadioOption[];
  label?: string;
  radiusItemProps?: RadioGroupItemProps;
  formLabel?: FormLabelProps;
}

export function RadioGroupField<T extends FieldValues>(
  props: RadioGroupField<T>,
) {
  const {
    control,
    fieldName,
    options,
    label,
    className,
    radiusItemProps,
    formLabel,
    ...rest
  } = props;
  return (
    <React.Fragment>
      <FormField
        control={control}
        name={fieldName}
        render={({ field }) => (
          <FormItem className="space-y-3">
            {label ? <FormLabel>{label}</FormLabel> : null}
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className={cn("flex flex-col space-y-1", className)}
                {...rest}
              >
                {options.map((option) => (
                  <FormItem key={option.value}>
                    <FormLabel className={cn("", formLabel?.className)}>
                      <FormControl>
                        <RadioGroupItem
                          value={option.value}
                          {...radiusItemProps}
                        />
                      </FormControl>
                      {option.label}
                    </FormLabel>
                  </FormItem>
                ))}
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </React.Fragment>
  );
}
