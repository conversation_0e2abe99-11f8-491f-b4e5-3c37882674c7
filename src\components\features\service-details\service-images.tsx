import { useTranslations } from "next-intl";
import { ServiceImage } from "./service-image";

interface ServiceImagesProps {
  images: string[];
  serviceName: string;
}

export function ServiceImages({ images, serviceName }: ServiceImagesProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <div className="flex flex-col w-full">
      <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
        {t("serviceImages")} :
      </label>
      <div className="flex justify-center items-center gap-12 w-full">
        {images && images.length > 0 ? (
          images
            .slice(0, 3)
            .map((imageUrl, index) => (
              <ServiceImage
                key={index}
                imageUrl={imageUrl}
                serviceName={serviceName}
                index={index}
              />
            ))
        ) : (
          <>
            <div className="w-[200px] h-[166px] bg-muted rounded-[10px] border border-border flex items-center justify-center">
              <span className="text-xs text-foreground font-['Poppins']">
                {t("noImages")}
              </span>
            </div>
            <div className="w-[200px] h-[166px] bg-muted rounded-[10px] border border-border flex items-center justify-center">
              <span className="text-xs text-foreground font-['Poppins']">
                {t("noImages")}
              </span>
            </div>
            <div className="w-[200px] h-[166px] bg-muted rounded-[10px] border border-border flex items-center justify-center">
              <span className="text-xs text-foreground font-['Poppins']">
                {t("noImages")}
              </span>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
