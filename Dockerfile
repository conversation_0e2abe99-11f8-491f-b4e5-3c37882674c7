# Base image
FROM node:23.5.0-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install pnpm 10.4.0 using npm
RUN npm install -g pnpm@10.4.0

# Install dependencies using pnpm if pnpm-lock.yaml exists
COPY package.json pnpm-lock.yaml* ./
RUN if [ -f pnpm-lock.yaml ]; then \
    pnpm install --frozen-lockfile; \
    else \
    echo "pnpm-lock.yaml not found." && exit 1; \
    fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Install pnpm in the builder stage
RUN npm install -g pnpm@10.4.0

# Copy package files first
COPY package.json pnpm-lock.yaml* ./

# Install dependencies in builder stage with verbose output
RUN if [ -f pnpm-lock.yaml ]; then \
    pnpm install --frozen-lockfile --verbose; \
    else \
    echo "pnpm-lock.yaml not found." && exit 1; \
    fi

# Debug: Check if Next.js is installed
RUN ls -la node_modules/next/dist/bin/ || echo "Next.js bin directory not found"

# Copy source code
COPY . .

# Add build-time environment variables
ARG NEXT_PUBLIC_BASE_API_URL
ENV NEXT_PUBLIC_BASE_API_URL=${NEXT_PUBLIC_BASE_API_URL}

# Build with more verbose output
RUN if [ -f pnpm-lock.yaml ]; then \
    pnpm run build; \
    else \
    echo "pnpm-lock.yaml not found." && exit 1; \
    fi

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next && chown nextjs:nodejs .next

# Copy standalone output and static files
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
