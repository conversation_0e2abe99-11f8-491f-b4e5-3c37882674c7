import { TableDateFilter } from "./table-date-filter";
import { TablePageSizeFilter } from "./table-page-size-filter";
import { TableSearchFilter } from "./table-search-filter";
import { TableStatusFilter } from "./table-status-filter";
import React from "react";

interface TableFiltersProps {
  table: {
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
    searchColumn: string;
    setSearchColumn: (value: string) => void;
    getColumn: (
      columnId: string,
    ) => NativeTableColumnInstance<Service> | undefined;
    resetFilters: () => void;
    resetSorting: () => void;
    reset: () => void;
  };
}

export function TableFilters({ table }: TableFiltersProps) {
  return (
    <div className="p-1">
      <div className="px-2.5 py-3 ">
        <div className="flex text-sm justify-between items-center">
          <TableSearchFilter table={table} />

          <div className="flex gap-4.5">
            <TableDateFilter table={table} />
            <TableStatusFilter table={table} />
            <TablePageSizeFilter />
          </div>
        </div>
      </div>
    </div>
  );
}
