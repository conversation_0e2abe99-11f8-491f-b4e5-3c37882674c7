import Image from "next/image";
import React from "react";

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <div className="flex flex-col justify-center align-center  min-h-screen">
        {/* Header */}
        <header className="bg-background border-b border-border">
          <div className="container m pr-6 py-4">
            <div className="flex items-start">
              <div className="w-56 h-10 relative">
                <Image
                  src="/logo.svg"
                  alt="AFreeServ"
                  fill
                  className="object-contain rounded-lg"
                />
              </div>
            </div>
          </div>
        </header>

        {/* Top Section - Children Content */}
        <div className="p-8 my-auto h-full">
          <div className="w-full max-w-md mx-auto">{children}</div>
        </div>

        {/* Footer */}
        <footer className="bg-background border-t border-border">
          <div className="container mx-auto px-6 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 relative">
                  <Image
                    src="/logo.jpg"
                    alt="AFreeServ"
                    fill
                    className="object-contain rounded-lg"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">
                    AFreeServ
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Professional Services Platform
                  </p>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} AFreeServ. All rights reserved.
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
