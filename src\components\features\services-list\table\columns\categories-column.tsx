import { TruncatedText } from "@/components/common";

export const categoriesColumn: NativeTableColumn<Service> = {
  id: "categories",
  accessorKey: "categories",
  header: "Category",
  enableSorting: true,
  cell: ({ value }) => {
    const categories = value as Category[];
    const categoryText = categories.map((cat) => cat.name).join(", ");
    return <TruncatedText maxWidth="150" text={categoryText} />;
  },
  size: 150,
};
