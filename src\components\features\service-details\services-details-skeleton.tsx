import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft } from "lucide-react";

export function ServiceDetailsSkeleton() {
  return (
    <div className="flex flex-col items-center gap-2.5 px-5 pb-2.5">
      {/* Navigation Header */}
      <div className="flex justify-between items-center w-full gap-14 px-2.5 py-2">
        <div className="flex items-center gap-2.5">
          <div className="flex justify-center items-center w-9 h-9 bg-background border border-border rounded-full">
            <ChevronLeft className="w-4 h-4 text-foreground" />
          </div>
          <Skeleton className="h-4 w-12" />
        </div>
        <div className="w-6 h-6" />
      </div>

      {/* Main Content Card */}
      <Card className="w-full bg-background rounded-t-[10px] border-0">
        <CardContent className="flex">
          {/* Provider Photo Section */}
          <div className="flex flex-1 flex-col justify-center items-center rounded-[10px]">
            <Skeleton className="w-[150px] h-[150px] rounded-full" />
          </div>
          <div className="flex-2 w-full flex">
            {/* Provider Information */}
            <div className="flex-1 w-full gap-2.5 flex flex-col">
              <div className="bg-secondary px-2.5 py-1 flex justify-between items-center">
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-3 w-32" />
              <Skeleton className="h-3 w-40" />
              <Skeleton className="h-3 w-28" />
            </div>

            {/* Service Popularity Section */}
            <div className="flex-1 w-full gap-2.5 flex flex-col">
              <div className="bg-secondary px-2.5 py-1 flex justify-between items-center">
                <Skeleton className="h-3 w-28" />
              </div>
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Service Details Section */}
      <Card className="w-full p-0 bg-background border-0">
        <CardHeader className="flex flex-col p-0">
          {/* Header with Status */}
          <div className="flex w-full p-3 items-center border-b border-border">
            <div className="flex-1">
              <Skeleton className="h-4 w-28" />
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center gap-2">
                <div className="flex flex-wrap gap-2">
                  <Skeleton className="h-3 w-12" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-5 w-16 rounded-md ml-2" />
              </div>
              <div className="flex items-center">
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col gap-6 px-5 py-2.5">
          {/* Creation Date */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-24 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1">
              <Skeleton className="h-3 w-32" />
            </div>
          </div>

          {/* Service Title */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-20 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1">
              <Skeleton className="h-3 w-48" />
            </div>
          </div>

          {/* Service Description */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-32 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1 min-h-[100px] space-y-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-4/5" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          </div>

          {/* Category */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-16 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1">
              <Skeleton className="h-3 w-24" />
            </div>
          </div>

          {/* Photos */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-12 mb-1" />
            <div className="flex justify-center items-center gap-12 w-full">
              {Array.from({ length: 3 }).map((_, index) => (
                <Skeleton key={index} className="w-48 h-40 rounded-lg" />
              ))}
            </div>
          </div>

          {/* Price */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-10 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1">
              <Skeleton className="h-3 w-16" />
            </div>
          </div>

          {/* Location */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-16 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1">
              <Skeleton className="h-3 w-32" />
            </div>
          </div>

          {/* About Provider */}
          <div className="flex flex-col">
            <Skeleton className="h-3 w-28 mb-1" />
            <div className="bg-background border border-border rounded-lg px-2.5 py-1 min-h-[80px] space-y-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center items-center gap-12 px-5 py-2.5">
            <Skeleton className="w-48 h-9 rounded-md" />
            <Skeleton className="w-48 h-9 rounded-md" />
            <Skeleton className="w-48 h-9 rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
