// Helper functions to reduce complexity
const isThisYear = (serviceDate: Date, now: Date): boolean => {
  return serviceDate.getFullYear() === now.getFullYear();
};

const isLastYear = (serviceDate: Date, now: Date): boolean => {
  return serviceDate.getFullYear() === now.getFullYear() - 1;
};

const isThisMonth = (serviceDate: Date, now: Date): boolean => {
  return (
    serviceDate.getFullYear() === now.getFullYear() &&
    serviceDate.getMonth() === now.getMonth()
  );
};

const isLastMonth = (serviceDate: Date, now: Date): boolean => {
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);
  return (
    serviceDate.getFullYear() === lastMonth.getFullYear() &&
    serviceDate.getMonth() === lastMonth.getMonth()
  );
};

const filterByDateRange = (
  serviceDate: Date,
  now: Date,
  filterValue: string,
): boolean => {
  switch (filterValue) {
    case "this-year":
      return isThisYear(serviceDate, now);
    case "last-year":
      return isLastYear(serviceDate, now);
    case "this-month":
      return isThisMonth(serviceDate, now);
    case "last-month":
      return isLastMonth(serviceDate, now);
    default:
      return true;
  }
};

export const createdAtColumn: NativeTableColumn<Service> = {
  id: "createdAt",
  accessorKey: "createdAt",
  header: "Created Date",
  enableSorting: true,
  enableFiltering: true,
  filterFn: (row, columnId, filterValue) => {
    if (!filterValue || filterValue === "" || filterValue === "all") {
      return true;
    }

    // Ensure filterValue is a string
    const filterString =
      typeof filterValue === "string" ? filterValue : String(filterValue);

    const createdAt = row.getValue(columnId) as string;
    if (!createdAt) return false;

    const serviceDate = new Date(createdAt);
    const now = new Date();

    return filterByDateRange(serviceDate, now, filterString);
  },
  cell: ({ value }) => {
    if (!value) return <span className="text-muted-foreground">-</span>;

    const date = new Date(value as string);
    return (
      <span className="text-sm">
        {date.toLocaleDateString("fr-FR", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })}
      </span>
    );
  },
  size: 140,
};
