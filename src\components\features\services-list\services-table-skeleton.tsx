import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import React from "react";

export function ServicesTableSkeleton() {
  return (
    <div className="bg-white rounded-[8px] border border-secondary overflow-hidden">
      {/* Filters skeleton */}
      <div className="p-1">
        <div className="px-2.5 py-3">
          <div className="flex justify-between items-center gap-14">
            <Skeleton className="h-10 w-80" />
            <div className="flex items-center gap-4.5">
              <Skeleton className="h-[28px] w-[160px]" />
              <Skeleton className="h-[28px] w-[160px]" />
              <Skeleton className="h-[28px] w-[160px]" />
            </div>
          </div>
        </div>
      </div>

      {/* Table skeleton */}
      <Table>
        <TableHeader className="bg-secondary">
          <TableRow>
            {Array.from({ length: 8 }).map((_, index) => (
              <TableHead key={index}>
                <Skeleton className="h-4 w-20" />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              {Array.from({ length: 8 }).map((_, cellIndex) => (
                <TableCell key={cellIndex}>
                  <Skeleton className="h-4 w-full" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination skeleton */}
      <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[282x] h-[48px]">
        <div className="flex items-center gap-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="w-6 h-6 rounded-[5px]" />
          ))}
        </div>
      </div>
    </div>
  );
}
