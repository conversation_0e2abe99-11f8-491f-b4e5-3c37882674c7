"use client";

import Image from "next/image";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface ServiceImageProps {
  imageUrl: string;
  serviceName: string;
  index: number;
}

export function ServiceImage({
  imageUrl,
  serviceName,
  index,
}: ServiceImageProps) {
  const t = useTranslations("ServiceDetails");
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (imageError) {
    return (
      <div className="w-full h-48 bg-muted rounded-lg flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="text-2xl mb-2">🖼️</div>
          <p className="text-sm">{t("imageLoadError")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-[200px] h-[166px] bg-muted rounded-[10px] border border-border overflow-hidden">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}

      <Image
        src={imageUrl}
        alt={`${serviceName} - ${t("image")} ${index + 1}`}
        fill
        className="object-cover transition-opacity duration-300"
        style={{ opacity: isLoading ? 0 : 1 }}
        onError={handleImageError}
        onLoad={handleImageLoad}
        sizes="200px"
      />
    </div>
  );
}
