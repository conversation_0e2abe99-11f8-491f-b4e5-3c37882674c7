"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";

import { FormExampleType, FormExampleSchema } from "@/schemas";
import React from "react";
import { useForm } from "react-hook-form";
import { Button, Form, FormLabel } from "../ui";
import { InputField } from "./form-fields/input-field";
import { IconMessage, IconSearch } from "@tabler/icons-react";
import { RadioGroupField, SelectField, TextareaField } from "./form-fields";

export function FormExample() {
  const form = useForm<FormExampleType>({
    resolver: zodResolver(FormExampleSchema),
  });

  function onFormSubmit(data: FormExampleType) {
    console.log({ data });
  }

  const FILTERS_OPTIONS = [
    {
      label: "Recent",
      value: "recent",
    },
    {
      label: "Old",
      value: "old",
    },
    {
      label: "Desc price",
      value: "desc_price_",
    },
    {
      label: "ASC price",
      value: "asc_price",
    },
  ];

  return (
    <Form {...form}>
      <form
        className="mt-10 space-y-5"
        onSubmit={form.handleSubmit(onFormSubmit)}
      >
        <InputField
          control={form.control}
          fieldName="input"
          label={"This is the input field example"}
          placeholder="Enter a text here"
          icon={{
            position: "start",
            icon: <IconSearch size={15} className="text-border" />,
          }}
        />
        <InputField
          control={form.control}
          fieldName="input"
          label={"This is the input field example"}
          placeholder="Enter a text here"
          icon={{
            position: "end",
            icon: <IconSearch size={15} className="text-border" />,
          }}
        />
        <InputField
          control={form.control}
          fieldName="input"
          label={"This is the input field example"}
          placeholder="Enter a text here"
          className="bg-input"
        />
        <SelectField
          control={form.control}
          fieldName="select"
          options={[
            {
              label: "Option one",
              value: "1",
            },
            {
              label: "Option two",
              value: "2",
            },
          ]}
          placeholder="Select a value"
          label="This is the select field example"
        />
        <SelectField
          control={form.control}
          fieldName="select"
          options={[
            {
              label: "Option one",
              value: "1",
            },
            {
              label: "Option two",
              value: "2",
            },
          ]}
          placeholder="Select a value"
          label="This is the select field example"
          useSearchField={false}
          className="bg-transparent"
        />
        <TextareaField
          control={form.control}
          field_name="textarea"
          placeholder="Enter you message"
          label="This is the textarea field example"
          icon={{
            position: "start",
            icon: <IconMessage size={15} className="text-border" />,
          }}
        />
        <TextareaField
          control={form.control}
          field_name="textarea"
          placeholder="Enter you message"
          label="This is the textarea field example"
          icon={{
            position: "end",
            icon: <IconMessage size={15} className="text-border" />,
          }}
        />
        <div className="space-y-1.5">
          <FormLabel>Filters</FormLabel>
          <RadioGroupField
            control={form.control}
            fieldName="sorting"
            options={FILTERS_OPTIONS}
            formLabel={{
              className:
                "text-sm flex flex-row-reverse items-center justify-between border border-input rounded-10 h-10 px-3 cursor-pointer",
            }}
          />
        </div>
        <TextareaField
          control={form.control}
          field_name="textarea"
          placeholder="Enter you message"
          label="This is the textarea field example"
          className="bg-input"
        />
        <Button>Hello AfreeSer</Button>
        <Button variant={"secondary"}>Hello AfreeSer</Button>
      </form>
    </Form>
  );
}
