// Colonnes natives converties depuis TanStack Table - Refactorisées en fichiers séparés
import {
  idColumn,
  descriptionColumn,
  categoriesColumn,
  statusColumn,
  providerColumn,
  createdAtColumn,
  actionsColumn,
} from "./columns";

export const servicesColumns: NativeTableColumn<Service>[] = [
  idColumn,
  descriptionColumn,
  categoriesColumn,
  statusColumn,
  providerColumn,
  createdAtColumn,
  actionsColumn,
];
