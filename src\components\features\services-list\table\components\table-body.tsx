import { TableBody, TableCell, TableRow } from "@/components";
import { TableBodyCell } from "@/components/common/table/table-body-cell";
import { useTranslations } from "next-intl";

interface ServicesTableBodyProps {
  table: {
    rows: NativeTableRow<Service>[];
    columns: NativeTableColumnInstance<Service>[];
  };
  columns: NativeTableColumn<Service>[];
}

export function ServicesTableBody({ table, columns }: ServicesTableBodyProps) {
  const t = useTranslations("Services");

  return (
    <TableBody>
      {table.rows?.length ? (
        table.rows.map((row) => (
          <TableRow key={row.id}>
            {table.columns.map((column) => (
              <TableBodyCell key={column.id} column={column} row={row} />
            ))}
          </TableRow>
        ))
      ) : (
        <TableRow>
          <TableCell colSpan={columns.length} className="h-24 text-center">
            {t("noResults")}
          </TableCell>
        </TableRow>
      )}
    </TableBody>
  );
}
